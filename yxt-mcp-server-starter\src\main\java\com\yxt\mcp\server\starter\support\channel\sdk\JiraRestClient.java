package com.yxt.mcp.server.starter.support.channel.sdk;

import com.fasterxml.jackson.databind.JsonNode;
import com.yxt.mcp.server.starter.support.channel.dto.response.JiraIssuesResponse;
import com.yxt.mcp.server.starter.support.channel.properties.MCPToolChannelProperties;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * @Description Jira 渠道 rest rpc client
 * @Date 2025/6/9 9:20
 * <AUTHOR>
 */
@Slf4j
@Component
public class JiraRestClient implements InitializingBean {

    private RestClient restClient;

    @Resource
    private MCPToolChannelProperties mcpToolChannelProperties;


    /**
     * 发起get请求
     *
     * @param url
     * @return
     */
    public String get(String url) {
        return restClient.get()
            .uri(url)
            .retrieve().body(String.class);
    }

    /**
     * 根据项目ID或键搜索项目信息 该方法通过调用REST API来获取项目信息，如果提供了项目ID或键，则将其追加到请求URL中
     *
     * @param projectIdOrKey 项目ID或键，用于指定特定项目如果未提供，则默认获取所有项目的信息
     * @return 返回项目的JSON格式信息字符串
     */
    public String searchProject(String projectIdOrKey) {
        // 初始化URL，用于构建项目搜索的API路径
        StringBuilder url = new StringBuilder("/rest/api/2/project");
        // 如果项目ID或键不为空，则将其追加到URL中，以获取特定项目的详细信息
        if (StringUtils.isNotBlank(projectIdOrKey)) {
            url.append("/").append(projectIdOrKey);
        }
        // 发起GET请求，通过构建的URL获取项目信息
        // 使用restClient进行HTTP请求，并指定返回类型为JsonNode
        JsonNode body = restClient.get()
            .uri(url.toString())
            .retrieve()
            .body(JsonNode.class);
        return body.toString();
    }


    /**
     * 根据JQL查询问题
     * <p>
     * 该方法使用提供的JQL（Jira Query Language）字符串和最大结果数量来构造一个URI， 然后调用REST API的/search端点来搜索问题此方法主要是为了方便用户能够
     * 根据不同的查询条件获取到相应的issue信息
     *
     * @param jql        JIRA查询语言字符串，用于指定搜索条件
     * @param maxResults 指定最多返回的问题数量
     * @return 返回包含搜索结果的字符串
     */
    public JiraIssuesResponse searchIssuesJql(String jql, Integer maxResults) {
        // 构建搜索用的URL，包含JQL查询和最大结果数
        String url = UriComponentsBuilder.fromPath("/rest/api/2/search")
            .queryParam("jql", jql)
            .queryParam("maxResults", maxResults)
            .build()
            .toUriString();
        try {
            // 发起GET请求，获取搜索结果，并将其解析为JsonNode
            JiraIssuesResponse body = restClient.get()
                .uri(url)
                .retrieve()
                .body(JiraIssuesResponse.class);
            // 将搜索结果的JsonNode转换为字符串并返回
            return body;
        } catch (Exception e) {
            log.error("JiraRestClient searchIssuesJql error: " + e.getMessage());
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.restClient = RestClient.builder()
            .baseUrl(mcpToolChannelProperties.getJira().getUrl())
            .defaultHeader("Authorization", "Bearer " + mcpToolChannelProperties.getJira().getToken())
            .defaultHeader("Content-Type", "application/json")
            .build();
    }
}
