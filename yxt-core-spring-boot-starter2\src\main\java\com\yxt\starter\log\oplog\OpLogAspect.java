package com.yxt.starter.log.oplog;

import com.yxt.lang.util.ExLogger;
import com.yxt.lang.util.ExprUtil;
import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Slf4j
public class OpLogAspect {

    private static final String USER_ID_HEADER_NAME = "userId";

    private static final String USER_NAME_HEADER_NAME = "userName";

    private static final String AUDIT_LOGGER_NAME = "AuditLog";

    private static final String OP_LOG_FIELD_VALUE = "OpLog";

    private static final String RETURN_VALUE_VARIABLE_NAME = "returnValue";

    @Value("${spring.application.name}")
    private String serviceName;

    @Around("@annotation(opLog)")
    public Object around(ProceedingJoinPoint pjp, OpLog opLog) throws Throwable {
        Map<String, Object> variableMap = OpLogContext.getVariables();
        if (StringUtils.isEmpty(opLog.content())) {
            return pjp.proceed();
        }
        String userId = null;
        String userName = null;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            userId = request.getHeader(USER_ID_HEADER_NAME);
            userName = request.getHeader(USER_NAME_HEADER_NAME);
        } catch (Throwable t) {
            // ignore~
        }

        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(userName)) {
            return pjp.proceed();
        }

        Object returnValue;
        Method method = ((MethodSignature)pjp.getSignature()).getMethod();
        Object[] args = pjp.getArgs();
        Boolean result = false;
        try {
            returnValue = pjp.proceed();
            if (returnValue != null) {
                variableMap.put(RETURN_VALUE_VARIABLE_NAME, returnValue);
                try {
                    result = Boolean.valueOf(ExprUtil.parseEL(opLog.resultMatchExpr(), method, args, variableMap));
                } catch (Throwable t) {
                    log.error(t.getMessage(), t);
                }
            }
        } finally {
            try {
                String name = opLog.name();
                if (ObjectUtils.isEmpty(name)) {
                    name = String.format("%s_%s_%s", serviceName, pjp.getTarget().getClass().getName(), method.getName());
                }
                String label = opLog.label();
                String content = ExprUtil.parseEL(opLog.content(), method, args, variableMap);
                String objectId = ExprUtil.parseEL(opLog.objectId(), method, args, variableMap);
                String merCode = ExprUtil.parseEL(opLog.merCode(), method, args, variableMap);
                String[] tagNames = opLog.tagNames();
                String[] tagValues = opLog.tagValues();
                Map<String, Object> tags = new HashMap<>();
                if (tagNames.length > 0) {
                    for (int i = 0; i < tagNames.length; i++) {
                        String parsedTagName = ExprUtil.parseEL(tagNames[i], method, args, variableMap);
                        String parsedTagValue = ExprUtil.parseEL(tagValues[i], method, args, variableMap);
                        tags.put(parsedTagName, parsedTagValue);
                    }
                }
                OpLogVO opLogVO = OpLogVO.builder().name(name).label(label).content(content).merCode(merCode).objectId(objectId).tags(tags).result(result).build();
                ExLogger.logger(AUDIT_LOGGER_NAME).field(OP_LOG_FIELD_VALUE).info(JSON.toJSONString(opLogVO));
            } catch (Throwable t) {
                log.error(t.getMessage(), t);
            }
        }
        return returnValue;
    }

    @Data
    @Builder
    @AllArgsConstructor
    private static class OpLogVO {

        private String name;

        private String label;

        private String userId;

        private String userName;

        private String content;

        private String merCode;

        private String objectId;

        private Boolean result;

        private Map<String, Object> tags;

    }
}
