package com.yxt.mcp.server.starter.support.session;

import cn.hutool.core.util.ReflectUtil;
import com.yxt.mcp.server.starter.auth.AuthorizationService;
import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.entrance.provider.YxtWebFluxSseServerTransportProvider.WebFluxMcpSessionTransport;
import com.yxt.mcp.server.starter.support.constants.AuthFieldConstants;
import com.yxt.mcp.server.starter.support.tool.YxtNacosMcpToolsInitializer;
import io.modelcontextprotocol.server.McpAsyncServer;
import io.modelcontextprotocol.server.McpAsyncServerExchange;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpError;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import io.modelcontextprotocol.spec.McpSchema.InitializeRequest;
import io.modelcontextprotocol.spec.McpSchema.InitializeResult;
import io.modelcontextprotocol.spec.McpSchema.ListToolsResult;
import io.modelcontextprotocol.spec.McpSchema.Root;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import io.modelcontextprotocol.spec.McpServerSession;
import io.modelcontextprotocol.spec.McpServerSession.InitRequestHandler;
import io.modelcontextprotocol.spec.McpServerSession.RequestHandler;
import io.modelcontextprotocol.spec.McpServerTransport;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.mcp.server.autoconfigure.McpServerProperties;
import org.springframework.beans.factory.ObjectProvider;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @Description 自定义session处理器
 * @Date 2025/06/16/11:02
 */
public class YxtSessionFactory implements McpServerSession.Factory {

    private static final Logger logger = LoggerFactory.getLogger(YxtNacosMcpToolsInitializer.class);


    private McpServerProperties serverProperties;


    private ObjectProvider<BiConsumer<McpSyncServerExchange, List<Root>>> rootsChangeConsumers;


    private Map<String, RequestHandler<?>> requestHandlers = new HashMap<>();

    private Map<String, McpServerSession.NotificationHandler> notificationHandlers = new HashMap();


    private AuthorizationService authorizationService;


    private InitRequestHandler initHandler;

    public YxtSessionFactory(
        ObjectProvider<BiConsumer<McpSyncServerExchange, List<Root>>> rootsChangeConsumers,
        McpServerProperties serverProperties, AuthorizationService authorizationService) {
        this.rootsChangeConsumers = rootsChangeConsumers;
        this.serverProperties = serverProperties;
        this.authorizationService = authorizationService;
    }


    @Override
    public McpServerSession create(McpServerTransport sessionTransport) {
        YxtAuthResult yxtAuthResult = null;
        String appId = Strings.EMPTY;
        String businessScenario = Strings.EMPTY;
        if (sessionTransport instanceof WebFluxMcpSessionTransport yxtTransport) {
            yxtAuthResult = yxtTransport.getYxtAuthResult();
            appId = yxtTransport.getAppId();
            businessScenario = yxtTransport.getBusinessScenario();

        }
        //liqiangtodo 这里在 MCP Server 写死到局部变量，但是handle构建非常复杂，我这里采用反射直接获取

        return new YxtMcpServerSession(UUID.randomUUID().toString(), serverProperties.getRequestTimeout(),
            sessionTransport,
            initHandler, Mono::empty, requestHandlers,
            notificationHandlers,
            yxtAuthResult, appId,
            businessScenario);
    }


    public void initializeHandlers(McpAsyncServer mcpAsyncServer) {

        //初始化requestHandle
        requestHandlers.put(McpSchema.METHOD_PING, (exchange, params) -> Mono.just(Map.of()));
        RequestHandler<ListToolsResult> defaultListToolHandle = getListToolRequestHandler(mcpAsyncServer,
            "toolsListRequestHandler");
        // requestHandlers.put(McpSchema.METHOD_TOOLS_LIST, defaultListToolHandle);

        //liqiangtodo 自定义扩展权限认证
        requestHandlers.put(McpSchema.METHOD_TOOLS_LIST, (exchange, params) -> {

            Mono<ListToolsResult> result = defaultListToolHandle.handle(exchange, params);
            return result.map(listToolsResult -> {
                List<Tool> tools = listToolsResult.tools();
                McpServerSession mcpServerSession = (McpServerSession) ReflectUtil.getFieldValue(exchange, "session");
                if (mcpServerSession instanceof YxtMcpServerSession yxtMcpServerSession) {
                    String yxtUserId =
                        yxtMcpServerSession.getYxtAuthResult() != null ? yxtMcpServerSession.getYxtAuthResult()
                            .yxtUserId()
                            : null;
                    String empCode =
                        yxtMcpServerSession.getYxtAuthResult() != null ? yxtMcpServerSession.getYxtAuthResult()
                            .yxtEmpCode()
                            : null;
                    String appId = yxtMcpServerSession.getAppId();
                    String businessScenario = yxtMcpServerSession.getBusinessScenario();
                    YxtAuthResult yxtAuthResult = authorizationService.parseInvokeAuthResult(appId, yxtUserId, empCode,
                        businessScenario, params);
                    List<Tool> authTool = authorizationService.listToolsByPermission(appId,
                        yxtAuthResult.businessScenario(),
                        yxtAuthResult,
                        tools);
                    return new ListToolsResult(authTool, null);
                }
                return new ListToolsResult(tools, null);
            });
        });

        // requestHandlers.put(McpSchema.METHOD_TOOLS_CALL, getCallToolRequestHandler(mcpAsyncServer, "toolsCallRequestHandler"));
        RequestHandler<CallToolResult> defaultCallToolHandler = getCallToolRequestHandler(
            mcpAsyncServer, "toolsCallRequestHandler");
        //liqiangtodo 自定义或者权限认证
        requestHandlers.put(McpSchema.METHOD_TOOLS_CALL, (exchange, params) -> {
            McpServerSession mcpServerSession = (McpServerSession) ReflectUtil.getFieldValue(exchange, "session");
            if (mcpServerSession instanceof YxtMcpServerSession yxtMcpServerSession) {
                String yxtUserId =
                    yxtMcpServerSession.getYxtAuthResult() != null ? yxtMcpServerSession.getYxtAuthResult().yxtUserId()
                        : null;
                String empCode =
                    yxtMcpServerSession.getYxtAuthResult() != null ? yxtMcpServerSession.getYxtAuthResult().yxtEmpCode()
                        : null;
                String appId = yxtMcpServerSession.getAppId();
                String businessScenario = yxtMcpServerSession.getBusinessScenario();
                YxtAuthResult yxtAuthResult = authorizationService.parseInvokeAuthResult(appId, yxtUserId, empCode,
                    businessScenario, params);
                String toolName = "";
                if (params instanceof Map<?, ?> paramsMap && paramsMap.containsKey("name")) {
                    LinkedHashMap<Object, Object> arguments = (LinkedHashMap) paramsMap.get("arguments");
                    Object nameValue = paramsMap.get("name");
                    if (nameValue != null) {
                        toolName = nameValue.toString();
                    }
                    arguments.put(AuthFieldConstants.APP_ID, appId);
                    arguments.put(AuthFieldConstants.BUSINESS_SCENARIO, yxtAuthResult.businessScenario());
                    arguments.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID, yxtAuthResult.yxtUserId());
                    arguments.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID, yxtAuthResult.yxtEmpCode());
                    arguments.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN, yxtAuthResult.operatorLogin());
                    arguments.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE, yxtAuthResult.operatorSource());
                    arguments.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME, yxtAuthResult.operatorName());
                }

                // 调用授权服务检查用户是否有权限调用该工具
                return authorizationService.checkCallToolPermission(appId, yxtAuthResult.businessScenario(),
                        yxtAuthResult,
                        toolName)
                    .flatMap(hasPermission -> {
                        if (hasPermission) {
                            // 用户有权限，继续执行工具调用
                            return defaultCallToolHandler.handle(exchange, params);
                        } else {
                            // 用户无权限，返回错误
                            return Mono.error(
                                new McpError("User does not have permission to call the tool: " + params));
                        }
                    });
            } else {
                return defaultCallToolHandler.handle(exchange, params).map(Function.identity());
            }

        });

        requestHandlers.put(McpSchema.METHOD_RESOURCES_LIST,
            getListToolRequestHandler(mcpAsyncServer, "resourcesListRequestHandler"));
        requestHandlers.put(McpSchema.METHOD_RESOURCES_READ,
            getListToolRequestHandler(mcpAsyncServer, "resourcesReadRequestHandler"));
        requestHandlers.put(McpSchema.METHOD_RESOURCES_TEMPLATES_LIST,
            getListToolRequestHandler(mcpAsyncServer, "resourceTemplateListRequestHandler"));

        //构建promptsHandle
        requestHandlers.put(McpSchema.METHOD_PROMPT_LIST,
            getPromptsHandle(mcpAsyncServer, "promptsListRequestHandler"));
        requestHandlers.put(McpSchema.METHOD_PROMPT_GET, getPromptsHandle(mcpAsyncServer, "promptsGetRequestHandler"));

        //构建loggerHandle
        requestHandlers.put(McpSchema.METHOD_LOGGING_SET_LEVEL,
            getLogHandle(mcpAsyncServer, "setLoggerRequestHandler"));

        //构建completionCompleteRequestHandler
        requestHandlers.put(McpSchema.METHOD_COMPLETION_COMPLETE,
            getCompletionCompleteRequestHandler(mcpAsyncServer, "completionCompleteRequestHandler"));

        //构建 notificationHandlers
        notificationHandlers.put(McpSchema.METHOD_NOTIFICATION_INITIALIZED, (exchange, params) -> Mono.empty());
        List<BiConsumer<McpSyncServerExchange, List<Root>>> rootsChangeHandlers = new ArrayList<>();
        rootsChangeConsumers.ifAvailable(consumer -> {
            rootsChangeHandlers.add((exchange, roots) -> consumer.accept(exchange, roots));
            logger.info("Registered roots change consumer");
        });
        List<BiFunction<McpAsyncServerExchange, List<Root>, Mono<Void>>> rootChangeConsumers = new ArrayList<>();
        for (var rootChangeConsumer : rootsChangeHandlers) {
            rootChangeConsumers.add((exchange, list) -> Mono
                .<Void>fromRunnable(() -> rootChangeConsumer.accept(new McpSyncServerExchange(exchange), list))
                .subscribeOn(Schedulers.boundedElastic()));
        }
        notificationHandlers.put(McpSchema.METHOD_NOTIFICATION_ROOTS_LIST_CHANGED,
            asyncRootsListChangedNotificationHandler(mcpAsyncServer, "asyncRootsListChangedNotificationHandler",
                rootChangeConsumers));

        this.initHandler = new InitRequestHandler() {
            @Override
            public Mono<InitializeResult> handle(InitializeRequest initializeRequest) {
                return ReflectUtil.invoke(mcpAsyncServer, "asyncInitializeRequestHandler", initializeRequest);
            }
        };
    }


    public McpServerSession.NotificationHandler asyncRootsListChangedNotificationHandler(McpAsyncServer mcpAsyncServer,
        String methodName,
        List<BiFunction<McpAsyncServerExchange, List<Root>, Mono<Void>>> rootsChangeConsumers) {
        return ReflectUtil.invoke(
            mcpAsyncServer, methodName, rootsChangeConsumers);
    }


    public RequestHandler<ListToolsResult> getListToolRequestHandler(
        McpAsyncServer mcpAsyncServer,
        String methodName) {
        return ReflectUtil.invoke(
            mcpAsyncServer, methodName);
    }

    public RequestHandler<CallToolResult> getCallToolRequestHandler(McpAsyncServer mcpAsyncServer,
        String methodName) {
        return ReflectUtil.invoke(
            mcpAsyncServer, methodName);
    }

    public RequestHandler<McpSchema.ListPromptsResult> getPromptsHandle(McpAsyncServer mcpAsyncServer,
        String methodName) {
        return ReflectUtil.invoke(
            mcpAsyncServer, methodName);
    }

    public RequestHandler<Object> getLogHandle(McpAsyncServer mcpAsyncServer,
        String methodName) {
        return ReflectUtil.invoke(mcpAsyncServer, methodName);
    }

    public RequestHandler<McpSchema.CompleteResult> getCompletionCompleteRequestHandler(
        McpAsyncServer mcpAsyncServer,
        String methodName) {
        return ReflectUtil.invoke(
            mcpAsyncServer, methodName);
    }


}
