package com.yxt.dynamic.routing.proxy;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/14 15:57
 */
public class MapEntry<K,V> implements Map.Entry<K,V>{
    public MapEntry(K key, V value) {
        this.key = key;
        this.value = value;
    }

    private K key;
    private V value;

    @Override
    public K getKey() {
        return this.key;
    }

    @Override
    public V getValue() {
        return this.value;
    }

    @Override
    public V setValue(V value) {
        return this.value=value;
    }
}
