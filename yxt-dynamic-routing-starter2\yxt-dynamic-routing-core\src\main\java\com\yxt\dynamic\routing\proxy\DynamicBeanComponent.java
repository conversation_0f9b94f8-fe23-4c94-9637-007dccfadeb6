package com.yxt.dynamic.routing.proxy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

@Slf4j
public class DynamicBeanComponent implements ApplicationContextAware {

    private static ApplicationContext applicationContext = null;

    public DynamicBeanComponent() {
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        if (null == applicationContext) {
            applicationContext = context;
        }
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * getBean:通过类型获取指定的Bean. <br/>
     *
     * @param name 名称
     * @return {@link Object}
     * <AUTHOR>
     * @date 2020年8月12日 下午7:40:51
     * @modifier zhucun
     * @modifyDate 2020年8月12日 下午7:40:51
     * @since JDK 1.8
     */
    public static Object getBean(String name) {
        try {
            if(getApplicationContext()==null){
                return null;
            }
            return getApplicationContext().getBean(name);
        }catch (Exception e){
            log.error("获取对象失败 name:{}",name);
        }
        return null;
    }

    /**
     * getBean:通过class获取指定的Bean. <br/>
     *
     * @param clazz 类
     * @return T
     * <AUTHOR>
     * @date 2020年8月12日 下午7:39:48
     * @modifier zhucun
     * @modifyDate 2020年8月12日 下午7:39:48
     * @since JDK 1.8
     */
    public static <T> T getBean(Class<T> clazz) {
        try {
            if(getApplicationContext()==null){
                return null;
            }
            return getApplicationContext().getBean(clazz);
        }catch (Exception e){
            log.error("获取对象失败 class:{}",clazz.toString());
        }
        return null;
    }

    /**
     * getBean:通过name，class返回指定的Bean. <br/>
     *
     * @param name  类名称
     * @param clazz 类
     * @return T
     * <AUTHOR>
     * @date 2020年8月12日 下午7:38:57
     * @modifier zhucun
     * @modifyDate 2020年8月12日 下午7:38:57
     * @since JDK 1.8
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        try {
            if(getApplicationContext()==null){
                return null;
            }
            return getApplicationContext().getBean(name, clazz);
        }catch (Exception e){
            log.error("获取对象失败 class:{} name:{}",clazz.toString(),name);
        }
        return null;
    }

}