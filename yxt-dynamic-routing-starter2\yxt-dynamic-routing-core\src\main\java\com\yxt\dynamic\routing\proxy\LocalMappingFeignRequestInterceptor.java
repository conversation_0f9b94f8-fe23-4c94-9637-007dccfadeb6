package com.yxt.dynamic.routing.proxy;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 基于本地配置的Feign请求拦截器，优先使用本地配置进行路由转发
 * <AUTHOR>
 * @Date 2024/12/1 10:00
 */
@Slf4j
public class LocalMappingFeignRequestInterceptor implements RequestInterceptor {

    private DynamicProperties dynamicProperties;
    private NacosDiscoveryClient nacosDiscoveryClient;
    private Random random = new Random();

    @Override
    public void apply(RequestTemplate template) {
        dynamicProperties = getDynamicProperties();
        if (dynamicProperties == null || !dynamicProperties.isEnable()) {
            log.debug("动态路由未启用或配置为空");
            return;
        }

        Map<String, String> localMappings = dynamicProperties.getLocalMappings();
        if (CollectionUtils.isEmpty(localMappings)) {
            log.debug("本地映射配置为空");
            return;
        }

        nacosDiscoveryClient = getNacosDiscoveryClient();

        try {
            // 获取当前请求的目标服务名
            String currentTarget = template.feignTarget().url();
            String serviceName = extractServiceName(currentTarget);

            if (StringUtils.isBlank(serviceName)) {
                log.debug("无法提取服务名，跳过处理: {}", currentTarget);
                return;
            }

            log.debug("当前请求目标: {}, 提取的服务名: {}", currentTarget, serviceName);

            // 使用参考代码的逻辑查找匹配的本地配置
            String targetMapping = getLocalMapping(serviceName, localMappings);
            if (StringUtils.isNotBlank(targetMapping)) {
                applyLocalMapping(template, serviceName, targetMapping);
                log.info("本地配置路由转发: {} -> {}", serviceName, targetMapping);
            } else {
                log.debug("未找到匹配的本地配置: {}", serviceName);
            }

        } catch (Exception e) {
            log.error("本地配置路由转发处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从目标URL中提取服务名
     */
    private String extractServiceName(String targetUrl) {
        if (StringUtils.isBlank(targetUrl)) {
            return null;
        }
        
        // 如果是http://service-name格式，直接提取服务名
        if (targetUrl.startsWith("http://") || targetUrl.startsWith("https://")) {
            try {
                URI uri = new URI(targetUrl);
                String host = uri.getHost();
                // 如果host是IP地址，则不处理
                if (isIpAddress(host)) {
                    return null;
                }
                return host;
            } catch (Exception e) {
                log.warn("解析目标URL失败: {}", targetUrl);
                return null;
            }
        }
        
        return targetUrl;
    }

    /**
     * 判断是否为IP地址
     */
    private boolean isIpAddress(String host) {
        if (StringUtils.isBlank(host)) {
            return false;
        }
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return Pattern.matches(ipPattern, host);
    }

    /**
     * 参考示例代码实现的本地映射查找逻辑
     */
    private String getLocalMapping(String targetServiceName, Map<String, String> localMappings) {
        if (localMappings == null || localMappings.isEmpty()) {
            return null;
        }

        log.debug("查找服务 {} 的本地映射配置，可用配置: {}", targetServiceName, localMappings);

        // 1. 精确匹配
        if (localMappings.containsKey(targetServiceName)) {
            String result = localMappings.get(targetServiceName);
            log.debug("找到精确匹配: {} -> {}", targetServiceName, result);
            return result;
        }

        // 2. 正则匹配
        String hosts = null;
        for (Map.Entry<String, String> entry : localMappings.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            log.debug("尝试正则匹配: pattern={}, target={}", key, targetServiceName);

            try {
                Pattern pattern = Pattern.compile(key);
                Matcher matcher = pattern.matcher(targetServiceName);
                boolean isMatch = matcher.find(); // 使用find()而不是matches()
                if (!isMatch) {
                    log.debug("正则匹配失败: {} 不匹配 {}", targetServiceName, key);
                    continue;
                }

                // 进行正则替换
                int groupCount = matcher.groupCount();
                for (int i = 1; i <= groupCount; i++) {
                    value = value.replace("$" + i, matcher.group(i));
                }
                hosts = value;
                log.debug("正则匹配成功: {} 匹配 {} -> {}", targetServiceName, key, hosts);
                break;
            } catch (Exception e) {
                log.warn("正则表达式处理失败: pattern={}, target={}, error={}", key, targetServiceName, e.getMessage());
            }
        }

        if (StringUtils.isNotBlank(hosts)) {
            return hosts;
        }

        log.debug("未找到匹配的本地映射配置: {}", targetServiceName);
        return null;
    }

    /**
     * 判断是否为正则表达式模式
     */
    private boolean isRegexPattern(String pattern) {
        return pattern.contains("(") || pattern.contains("[") || pattern.contains("*") 
                || pattern.contains("+") || pattern.contains("?") || pattern.contains(".");
    }

    /**
     * 应用本地映射配置
     */
    private void applyLocalMapping(RequestTemplate template, String sourceService, String targetMapping) {
        try {
            log.debug("应用本地映射: {} -> {}", sourceService, targetMapping);

            // 处理多个目标（逗号分隔），选择第一个
            String selectedTarget = selectFirstTarget(targetMapping);

            // 如果目标是IP:端口格式
            if (isIpPortFormat(selectedTarget)) {
                String targetUrl = "http://" + selectedTarget;
                template.target(targetUrl);
                log.info("应用IP:端口映射: {} -> {}", sourceService, targetUrl);
            }
            // 如果目标是完整URL格式
            else if (selectedTarget.startsWith("http://") || selectedTarget.startsWith("https://")) {
                template.target(selectedTarget);
                log.info("应用URL映射: {} -> {}", sourceService, selectedTarget);
            }
            // 如果目标包含域名格式（包含.但不是IP地址）
            else if (isDomainFormat(selectedTarget)) {
                String targetUrl = "http://" + selectedTarget;
                template.target(targetUrl);
                log.info("应用域名映射: {} -> {}", sourceService, targetUrl);
            }
            // 如果目标是服务名格式
            else {
                applyServiceNameMapping(template, sourceService, selectedTarget);
            }
        } catch (Exception e) {
            log.error("应用本地映射失败: source={}, target={}, error={}",
                    sourceService, targetMapping, e.getMessage(), e);
        }
    }

    /**
     * 从目标配置中选择第一个目标（支持多目标用逗号分隔）
     */
    private String selectFirstTarget(String targetMapping) {
        if (StringUtils.isBlank(targetMapping)) {
            return null;
        }

        // 处理逗号分隔的多个目标
        targetMapping = StringUtils.trim(targetMapping);
        targetMapping = targetMapping.replace("，", ","); // 处理中文逗号

        String[] targets = targetMapping.split(",");
        if (targets.length > 0) {
            String selected = StringUtils.trim(targets[0]);
            log.debug("从多个目标中选择第一个: {} -> {}", targetMapping, selected);
            return selected;
        }

        return targetMapping;
    }

    /**
     * 判断是否为IP:端口格式
     */
    private boolean isIpPortFormat(String target) {
        if (StringUtils.isBlank(target)) {
            return false;
        }
        String ipPortPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]+$";
        return Pattern.matches(ipPortPattern, target);
    }

    /**
     * 判断是否为域名格式（包含.但不是IP地址）
     */
    private boolean isDomainFormat(String target) {
        if (StringUtils.isBlank(target)) {
            return false;
        }
        // 包含.但不是IP地址，且不包含端口号
        return target.contains(".") && !isIpAddress(target.split(":")[0]) && !isIpPortFormat(target);
    }

    /**
     * 应用服务名映射
     */
    private void applyServiceNameMapping(RequestTemplate template, String sourceService, String targetService) {
        log.debug("尝试应用服务名映射: {} -> {}", sourceService, targetService);

        if (nacosDiscoveryClient != null) {
            try {
                log.debug("通过Nacos查找服务实例: {}", targetService);
                List<ServiceInstance> instances = nacosDiscoveryClient.getInstances(targetService);
                if (!CollectionUtils.isEmpty(instances)) {
                    // 随机选择一个实例
                    ServiceInstance selectedInstance = instances.get(random.nextInt(instances.size()));
                    String targetUrl = selectedInstance.getUri().toString();
                    template.target(targetUrl);
                    log.info("应用服务名映射成功: {} -> {} ({})", sourceService, targetService, targetUrl);
                } else {
                    log.warn("目标服务 {} 没有可用实例", targetService);
                }
            } catch (Exception e) {
                log.error("获取目标服务实例失败: targetService={}, error={}", targetService, e.getMessage(), e);
            }
        } else {
            log.warn("NacosDiscoveryClient为空，无法进行服务发现");
        }
    }

    private DynamicProperties getDynamicProperties() {
        if (dynamicProperties == null) {
            dynamicProperties = DynamicBeanComponent.getBean(DynamicProperties.class);
        }
        return dynamicProperties;
    }

    private NacosDiscoveryClient getNacosDiscoveryClient() {
        if (nacosDiscoveryClient == null) {
            nacosDiscoveryClient = DynamicBeanComponent.getBean(NacosDiscoveryClient.class);
        }
        return nacosDiscoveryClient;
    }
}
