package com.yxt.dynamic.routing.proxy;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.CollectionUtils;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 基于本地配置的Feign请求拦截器，优先使用本地配置进行路由转发
 * <AUTHOR>
 * @Date 2024/12/1 10:00
 */
@Slf4j
public class LocalMappingFeignRequestInterceptor implements RequestInterceptor {

    private DynamicProperties dynamicProperties;
    private NacosDiscoveryClient nacosDiscoveryClient;
    private Random random = new Random();

    @Override
    public void apply(RequestTemplate template) {
        dynamicProperties = getDynamicProperties();
        if (dynamicProperties == null || !dynamicProperties.isEnable()) {
            log.debug("动态路由未启用或配置为空");
            return;
        }

        Map<String, String> localMappings = dynamicProperties.getLocalMappings();
        if (CollectionUtils.isEmpty(localMappings)) {
            log.debug("本地映射配置为空");
            return;
        }

        nacosDiscoveryClient = getNacosDiscoveryClient();

        try {
            // 获取当前请求的目标服务名
            String currentTarget = template.feignTarget().url();
            String serviceName = extractServiceName(currentTarget);

            if (StringUtils.isBlank(serviceName)) {
                log.debug("无法提取服务名，跳过处理: {}", currentTarget);
                return;
            }

            log.debug("当前请求目标: {}, 提取的服务名: {}", currentTarget, serviceName);

            // 查找匹配的本地配置
            String targetMapping = findMatchingMapping(serviceName, localMappings);
            if (StringUtils.isNotBlank(targetMapping)) {
                applyLocalMapping(template, serviceName, targetMapping);
                log.info("本地配置路由转发: {} -> {}", serviceName, targetMapping);
            } else {
                log.debug("未找到匹配的本地配置: {}", serviceName);
            }

        } catch (Exception e) {
            log.error("本地配置路由转发处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从目标URL中提取服务名
     */
    private String extractServiceName(String targetUrl) {
        if (StringUtils.isBlank(targetUrl)) {
            return null;
        }
        
        // 如果是http://service-name格式，直接提取服务名
        if (targetUrl.startsWith("http://") || targetUrl.startsWith("https://")) {
            try {
                URI uri = new URI(targetUrl);
                String host = uri.getHost();
                // 如果host是IP地址，则不处理
                if (isIpAddress(host)) {
                    return null;
                }
                return host;
            } catch (Exception e) {
                log.warn("解析目标URL失败: {}", targetUrl);
                return null;
            }
        }
        
        return targetUrl;
    }

    /**
     * 判断是否为IP地址
     */
    private boolean isIpAddress(String host) {
        if (StringUtils.isBlank(host)) {
            return false;
        }
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return Pattern.matches(ipPattern, host);
    }

    /**
     * 查找匹配的本地映射配置
     */
    private String findMatchingMapping(String serviceName, Map<String, String> localMappings) {
        log.debug("查找服务 {} 的本地映射配置，可用配置: {}", serviceName, localMappings);

        // 1. 首先查找精确匹配
        if (localMappings.containsKey(serviceName)) {
            String result = localMappings.get(serviceName);
            log.debug("找到精确匹配: {} -> {}", serviceName, result);
            return result;
        }

        // 2. 查找正则表达式匹配
        for (Map.Entry<String, String> entry : localMappings.entrySet()) {
            String pattern = entry.getKey();
            String replacement = entry.getValue();

            log.debug("尝试匹配模式: {} 对服务: {}", pattern, serviceName);

            // 检查是否为正则表达式（包含特殊字符）
            if (isRegexPattern(pattern)) {
                try {
                    Pattern regex = Pattern.compile(pattern);
                    Matcher matcher = regex.matcher(serviceName);
                    if (matcher.matches()) {
                        // 如果replacement包含$1等替换符，进行正则替换
                        String result;
                        if (replacement.contains("$")) {
                            result = matcher.replaceAll(replacement);
                        } else {
                            result = replacement;
                        }
                        log.debug("正则匹配成功: {} 匹配 {} -> {}", serviceName, pattern, result);
                        return result;
                    } else {
                        log.debug("正则匹配失败: {} 不匹配 {}", serviceName, pattern);
                    }
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: pattern={}, serviceName={}, error={}",
                            pattern, serviceName, e.getMessage());
                }
            } else {
                log.debug("跳过非正则模式: {}", pattern);
            }
        }

        log.debug("未找到匹配的本地映射配置: {}", serviceName);
        return null;
    }

    /**
     * 判断是否为正则表达式模式
     */
    private boolean isRegexPattern(String pattern) {
        return pattern.contains("(") || pattern.contains("[") || pattern.contains("*") 
                || pattern.contains("+") || pattern.contains("?") || pattern.contains(".");
    }

    /**
     * 应用本地映射配置
     */
    private void applyLocalMapping(RequestTemplate template, String sourceService, String targetMapping) {
        try {
            // 如果目标是IP:端口格式
            if (isIpPortFormat(targetMapping)) {
                String targetUrl = "http://" + targetMapping;
                template.target(targetUrl);
                log.debug("应用IP:端口映射: {} -> {}", sourceService, targetUrl);
            }
            // 如果目标是完整URL格式
            else if (targetMapping.startsWith("http://") || targetMapping.startsWith("https://")) {
                template.target(targetMapping);
                log.debug("应用URL映射: {} -> {}", sourceService, targetMapping);
            }
            // 如果目标包含域名格式（包含.但不是IP地址）
            else if (isDomainFormat(targetMapping)) {
                String targetUrl = "http://" + targetMapping;
                template.target(targetUrl);
                log.debug("应用域名映射: {} -> {}", sourceService, targetUrl);
            }
            // 如果目标是服务名格式
            else {
                applyServiceNameMapping(template, sourceService, targetMapping);
            }
        } catch (Exception e) {
            log.error("应用本地映射失败: source={}, target={}, error={}",
                    sourceService, targetMapping, e.getMessage(), e);
        }
    }

    /**
     * 判断是否为IP:端口格式
     */
    private boolean isIpPortFormat(String target) {
        if (StringUtils.isBlank(target)) {
            return false;
        }
        String ipPortPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]+$";
        return Pattern.matches(ipPortPattern, target);
    }

    /**
     * 判断是否为域名格式（包含.但不是IP地址）
     */
    private boolean isDomainFormat(String target) {
        if (StringUtils.isBlank(target)) {
            return false;
        }
        // 包含.但不是IP地址，且不包含端口号
        return target.contains(".") && !isIpAddress(target.split(":")[0]) && !isIpPortFormat(target);
    }

    /**
     * 应用服务名映射
     */
    private void applyServiceNameMapping(RequestTemplate template, String sourceService, String targetService) {
        log.debug("尝试应用服务名映射: {} -> {}", sourceService, targetService);

        if (nacosDiscoveryClient != null) {
            try {
                log.debug("通过Nacos查找服务实例: {}", targetService);
                List<ServiceInstance> instances = nacosDiscoveryClient.getInstances(targetService);
                if (!CollectionUtils.isEmpty(instances)) {
                    // 随机选择一个实例
                    ServiceInstance selectedInstance = instances.get(random.nextInt(instances.size()));
                    String targetUrl = selectedInstance.getUri().toString();
                    template.target(targetUrl);
                    log.info("应用服务名映射成功: {} -> {} ({})", sourceService, targetService, targetUrl);
                } else {
                    log.warn("目标服务 {} 没有可用实例", targetService);
                }
            } catch (Exception e) {
                log.error("获取目标服务实例失败: targetService={}, error={}", targetService, e.getMessage(), e);
            }
        } else {
            log.warn("NacosDiscoveryClient为空，无法进行服务发现");
        }
    }

    private DynamicProperties getDynamicProperties() {
        if (dynamicProperties == null) {
            dynamicProperties = DynamicBeanComponent.getBean(DynamicProperties.class);
        }
        return dynamicProperties;
    }

    private NacosDiscoveryClient getNacosDiscoveryClient() {
        if (nacosDiscoveryClient == null) {
            nacosDiscoveryClient = DynamicBeanComponent.getBean(NacosDiscoveryClient.class);
        }
        return nacosDiscoveryClient;
    }
}
