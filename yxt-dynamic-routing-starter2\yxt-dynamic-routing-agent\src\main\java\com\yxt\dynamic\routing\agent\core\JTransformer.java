package com.yxt.dynamic.routing.agent.core;

import javassist.CannotCompileException;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.NotFoundException;

import java.io.IOException;
import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.security.ProtectionDomain;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/21 18:11
 */
public class JTransformer implements ClassFileTransformer {

    private Map<String, JClass> jClassMap = new HashMap<>();
    private JSpring jSpring;

    public static JTransformer create() {
        JTransformer jTransformer = new JTransformer();
        return jTransformer;
    }

    public JClass jClass(String className) {
        String loadClassName = className.replace(".", "/");
        JClass jClass = new JClass(className, loadClassName, this);

        jClassMap.put(loadClassName, jClass);
        return jClass;
    }

    WriteClassComponent writeClassComponent = new WriteClassComponent();

    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined, ProtectionDomain protectionDomain, byte[] classfileBuffer) throws IllegalClassFormatException {
        if (jClassMap.containsKey(className)) {
            JClass jClass = jClassMap.get(className);
            ClassPool pool = ClassPool.getDefault();
            try {
                CtClass ctClass = pool.get(jClass.getClassName());
                ctClass = jClass.apply(ctClass);
                byte[] bytecode = ctClass.toBytecode();
                writeClassComponent.write(bytecode, jClass, protectionDomain);
                return bytecode;
            } catch (NotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            } catch (CannotCompileException e) {
                throw new RuntimeException(e);
            }
        }
        return classfileBuffer;
    }

    public JSpring jSpring() {
        if(this.jSpring==null){
            JSpring jSpring = new JSpring(this);
            this.jSpring = jSpring;
            writeClassComponent.writeJSpring(jSpring);
        }
        return jSpring;
    }
}
