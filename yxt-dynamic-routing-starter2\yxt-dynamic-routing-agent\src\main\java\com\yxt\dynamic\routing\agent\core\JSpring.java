package com.yxt.dynamic.routing.agent.core;

import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;

import java.util.HashSet;
import java.util.Set;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/31 17:13
 */
public class JSpring {

    Set<String> beanNames = new HashSet<>();

    JTransformer jTransformer;
    public JSpring(JTransformer jTransformer) {
        this.jTransformer=jTransformer;
    }

    public JTransformer then() {
        return jTransformer;
    }

    public JSpring excludeBean(String beanName) {
        beanNames.add(beanName);
        return this;
    }

    private static void addAnnotation(CtClass ctClass, String type){
        // 创建一个新的ConstPool（如果需要的话）

        ConstPool constPool = ctClass.getClassFile().getConstPool();
        // 过滤掉特定注解
        AnnotationsAttribute newAnnotationsAttr = new AnnotationsAttribute(constPool, AnnotationsAttribute.visibleTag);
        newAnnotationsAttr.addAnnotation(new Annotation(type,constPool));
        ctClass.getClassFile().addAttribute(newAnnotationsAttr);
    }

    private String beanName ="JacksonBeanExclusionPostProcessor";

    /**
     * @param packageName  包名，不含类名
     * @return
     */
    byte[] createBeanExclusionPostProcessorClass(String packageName) {
        try {
            // 创建ClassPool对象
            ClassPool pool = ClassPool.getDefault();

            // 定义要生成的类名
            String className = packageName+"."+beanName;

            // 创建CtClass对象，相当于创建一个类
            CtClass ctClass = pool.makeClass(className);

            // 添加实现的接口
            ctClass.addInterface(pool.get("org.springframework.beans.factory.config.BeanFactoryPostProcessor"));
            addAnnotation(ctClass, "org.springframework.stereotype.Component");


            // 添加方法：postProcessBeanFactory
            CtMethod postProcessBeanFactoryMethod = new CtMethod(
                    pool.get(void.class.getName()),
                    "postProcessBeanFactory",
                    new CtClass[]{pool.get("org.springframework.beans.factory.config.ConfigurableListableBeanFactory")},
                    ctClass
            );

            StringBuilder sb = new StringBuilder();
            for (String beanName : beanNames) {
                sb.append("\"").append(beanName).append("\",");
            }
            sb.setLength(sb.length() - 1);
            StringBuilder methodBody = new StringBuilder();
            methodBody.append("{\n")
                    .append("       String[] beanNameList = new String[]{").append(sb).append("}; \n")
                    .append("       int i = beanNameList.length; \n");

            methodBody
                    .append("    while (i>0) {\n")
                    .append("       i--;\n")
                    .append("       String beanName = beanNameList[i];\n")
                    .append("       if ($1.containsBeanDefinition(beanName)) {\n")
                    .append("         org.springframework.beans.factory.support.DefaultListableBeanFactory registry = (org.springframework.beans.factory.support.DefaultListableBeanFactory) $1;\n")
                    .append("         registry.removeBeanDefinition(beanName);\n")
                    .append("        }\n")
                    .append("    }\n")
                    .append("}\n");
            postProcessBeanFactoryMethod.setBody(methodBody.toString());

            ctClass.addMethod(postProcessBeanFactoryMethod);
            return ctClass.toBytecode();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

    String getFileName() {
        return beanName+".class";
    }
}
