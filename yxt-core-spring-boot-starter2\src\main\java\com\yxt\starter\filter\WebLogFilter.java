package com.yxt.starter.filter;

import com.yxt.lang.util.HeadersUtil;
import com.yxt.lang.util.JsonUtils;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@WebFilter(urlPatterns = "/*", asyncSupported = true)
@ConditionalOnProperty(prefix = "web-log-filter", value = {"enable"}, havingValue = "true")
public class WebLogFilter implements Filter {

    @Autowired
    private WebLogFilterConfig webLogFilterConfig;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException {
        HttpServletRequest httpServletRequest = ((HttpServletRequest) servletRequest);
        String userId = httpServletRequest.getHeader(HeadersUtil.USER_ID_HEADER_NAME);
        String merCode = httpServletRequest.getHeader(HeadersUtil.MER_CODE_HEADER_NAME);
        try {
            if (StringUtils.isNotBlank(userId)) {
                MDC.put(HeadersUtil.USER_ID_HEADER_NAME, userId);
            }
            if (StringUtils.isNotBlank(merCode)) {
                MDC.put(HeadersUtil.MER_CODE_HEADER_NAME, merCode);
            }

            String uri = httpServletRequest.getRequestURI();

            BodyHttpServletResponseWrapper responseWrapper = new BodyHttpServletResponseWrapper(
                (HttpServletResponse) servletResponse);

            long beginTime = System.currentTimeMillis();
            // 请求参数打印开关开启且uri在请求参数打印白名单中，打印请求参数日志
            if (webLogFilterConfig.getRequestEnable() && this.isNeedReqUri(uri)) {
                this.requestDoFilter(servletRequest, filterChain, httpServletRequest, uri, responseWrapper);
            } else {
                BodyHttpServletRequestWrapper requestWrapper = new BodyHttpServletRequestWrapper(
                    (HttpServletRequest) servletRequest);
                filterChain.doFilter(requestWrapper, responseWrapper);
            }

            long duration = System.currentTimeMillis() - beginTime;
            // 响应参数打印开关开启且uri在响应参数打印白名单中，打印响应参数日志
            if (webLogFilterConfig.getResponseEnable() && this.isNeedResUri(uri)) {
                this.responseDoFilter(uri, responseWrapper, duration);
            }
        } finally {
            if (StringUtils.isNotBlank(userId)) {
                MDC.remove(HeadersUtil.USER_ID_HEADER_NAME);
            }
            if (StringUtils.isNotBlank(merCode)) {
                MDC.remove(HeadersUtil.MER_CODE_HEADER_NAME);
            }
        }
    }

    /**
     * 判断是否需要打印响应参数
     *
     * @param servletRequest     请求
     * @param filterChain        过滤器链
     * @param httpServletRequest http请求
     * @param uri                请求路径
     * @param responseWrapper    响应包装类
     * @throws IOException      IO异常
     * @throws ServletException Servlet异常
     */
    private void requestDoFilter(ServletRequest servletRequest, FilterChain filterChain,
        HttpServletRequest httpServletRequest, String uri, BodyHttpServletResponseWrapper responseWrapper)
        throws IOException, ServletException {
        String contentType = httpServletRequest.getContentType();
        boolean multipart = StringUtils.isNotBlank(contentType) && contentType.contains("multipart/form-data");
        if (multipart) {
            // 文件上传，只打印基本参数
            if (log.isInfoEnabled()) {
                HashMap<String, String> headerMap = this.getHeaderMap(httpServletRequest);
                String url = String.valueOf(httpServletRequest.getRequestURL());
                log.info("当前访问路径：{}, url: {}, 方法：{}, authorization:{}", uri, url,
                    ((HttpServletRequest) servletRequest).getMethod(), headerMap);
            }
            filterChain.doFilter(servletRequest, responseWrapper);
        } else {
            BodyHttpServletRequestWrapper requestWrapper = new BodyHttpServletRequestWrapper(
                (HttpServletRequest) servletRequest);
            byte[] requestBody = requestWrapper.getBody();
            HashMap<String, String> headerMap = this.getHeaderMap(requestWrapper);
            if (log.isInfoEnabled()) {
                String url = String.valueOf(httpServletRequest.getRequestURL());
                log.info("当前访问路径：{}, url: {}, 方法：{}, 请求参数：{}, 请求头:{}", uri, url,
                    ((HttpServletRequest) servletRequest).getMethod(), byteToUtf8String(requestBody),
                    JsonUtils.toJson(headerMap));
            }
            filterChain.doFilter(requestWrapper, responseWrapper);
        }
    }

    /**
     * 判断是否需要打印响应参数
     *
     * @param uri             请求路径
     * @param responseWrapper 响应结果
     * @param duration        耗时
     */
    private void responseDoFilter(String uri, BodyHttpServletResponseWrapper responseWrapper, long duration) {
        if (log.isInfoEnabled()) {
            String responseStr = byteToUtf8String(responseWrapper.getBody());
            log.info("当前访问路径：{}, 响应结果：{}, 耗时：{}ms", uri, responseStr.substring(0,
                webLogFilterConfig.getPrintLogLength() > responseStr.length() ? responseStr.length()
                    : webLogFilterConfig.getPrintLogLength()), duration);
        } else {
            log.info("当前访问路径：{}, 耗时：{}ms", uri, duration);
        }
    }

    private HashMap<String, String> getHeaderMap(HttpServletRequest httpServletRequest) {
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        HashMap<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = httpServletRequest.getHeader(headerName);
            headerMap.put(headerName, headerValue);
        }
        return headerMap;
    }

    @Override
    public void destroy() {

    }

    private String byteToUtf8String(byte[] buffer) {
        return new String(buffer, StandardCharsets.UTF_8);
    }

    /**
     * 判断给定的URI是否需要记录请求参数日志
     *
     * @param uri 指定URI
     * @return true:需要记录请求参数日志，false:不需要记录请求参数日志
     */
    private Boolean isNeedReqUri(String uri) {
        List<String> antPatternUriList = this.webLogFilterConfig.getIncludeRequestPatternUris();
        if (CollectionUtils.isEmpty(antPatternUriList)) {
            return Boolean.FALSE;
        }
        PathMatcher antPathMatcher = new AntPathMatcher();
        return antPatternUriList.stream().anyMatch(pattern -> antPathMatcher.match(pattern, uri));
    }

    /**
     * 判断给定的URI是否需要记录响应结果日志
     *
     * @param uri 指定URI
     * @return true:需要记录响应结果日志，false:不需要记录响应结果日志
     */
    private Boolean isNeedResUri(String uri) {
        List<String> antPatternUriList = this.webLogFilterConfig.getIncludeResponsePatternUris();
        if (CollectionUtils.isEmpty(antPatternUriList)) {
            return Boolean.FALSE;
        }
        PathMatcher antPathMatcher = new AntPathMatcher();
        return antPatternUriList.stream().anyMatch(pattern -> antPathMatcher.match(pattern, uri));
    }
}
