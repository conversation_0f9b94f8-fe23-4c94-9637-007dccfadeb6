package com.yxt.starter.metrics.register;

import com.yxt.starter.metrics.MetricsBinderRegister;
import com.yxt.starter.metrics.ext.binder.FeignMetricsBinder;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import java.util.Map;
import okhttp3.OkHttpClient;
import org.springframework.context.ApplicationContext;

/**
 * feign监控
 *
 * <AUTHOR>
 * @date 2025/03/10 18:05
 **/
public class FeignClientMetricsBinderRegister implements MetricsBinderRegister {


    @Override
    public void registry(MeterRegistry meterRegistry, ApplicationContext context) {
        try {
            // 获取 Feign 客户端的连接池信息
            Map<String, OkHttpClient> okHttpClientMap = context.getBeansOfType(OkHttpClient.class);
            if (!okHttpClientMap.isEmpty()) {
                okHttpClientMap.forEach((key, value) -> {
                    // 创建 Feign 客户端连接池监控指标
                    FeignMetricsBinder metrics = new FeignMetricsBinder(value, Tags.of("feign_client", key));
                    metrics.bindTo(meterRegistry);
                });
            }
        } catch (Throwable e) {
            // 忽略异常
        }
    }
}
