### 前言
grey相关包有两个不兼容版本，根据代码内容，猜测是因为springboot大版本不一致，所以拆分成了两个，如下。为了方便开发单独将pa版本独立成一个git项目进行管理。并在动态代理版本上规范了【artifactId】【包名称】【版本号】等。本项目springboot高版本支持的版本。

springboot低版本支持
```
grey-spring-boot-web-starter  3.0.0-SNAPSHOT     grey-spring-lib 3.0.0-SNAPSHOT
grey-spring-boot-web-starter  3.1.0-SNAPSHOT     grey-spring-lib 3.2.0-SNAPSHOT
grey-spring-boot-web-starter  3.1.0-SNAPSHOT     grey-spring-lib 3.3.0-SNAPSHOT 
grey-spring-boot-web-starter  3.1.0-SNAPSHOT     grey-spring-lib 3.4.0-SNAPSHOT
grey-spring-boot-web-starter  3.2.0-SNAPSHOT     grey-spring-lib 3.5.0-SNAPSHOT 
```

springboot高版本支持
```
grey-spring-boot-web-starter  2.0.0.pa-SNAPSHOT  grey-spring-lib 3.1.0-<PERSON><PERSON>PSH<PERSON>
grey-spring-boot-web-starter  2.0.0.pa-SNAPSHOT  grey-spring-lib 3.1.0.X-SNAPSHOT
```

### 项目地址
https://yxtgit.hxyxt.com/java-global/yxt-dynamic-routing-starter

### 包划分描述
[grey-pa-spring-boot-gateway-starter](grey-pa-spring-boot-gateway-starter):  网关自动装配

[grey-pa-spring-boot-lib](grey-pa-spring-boot-lib)： 具体业务实现

[grey-pa-spring-boot-web-starter](grey-pa-spring-boot-web-starter) ： 服务自动装配



## 版本说明

### 初始版本

**yxt-dynamic-routing-starter**：
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-starter</artifactId>
    <version>2.0.0.pa-SNAPSHOT</version>
</dependency>
```
**[grey-pa-spring-boot-gateway-starter](grey-spring-boot-gateway-starter)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-gateway-starter</artifactId>
    <version>2.0.0.pa-SNAPSHOT</version>
</dependency>
```

**[grey-pa-spring-boot-web-starter](grey-pa-spring-boot-web-starter)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-web-starter</artifactId>
    <version>3.1.0-SNAPSHOT</version>
</dependency>
```
**[grey-pa-spring-boot-lib](grey-pa-spring-boot-lib)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-lib</artifactId>
    <version>3.1.0-SNAPSHOT</version>
</dependency>
```

### [grey-pa-spring-boot-lib](grey-pa-spring-boot-lib) 升级
升级内容
1. 网关中grey.local-mappings没有配置时,通过nacos获取具体ip进行路由
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-lib</artifactId>
    <version>3.1.0.x-SNAPSHOT</version>
</dependency>
```

### 动态路由升级
升级内容
1. 规范artifactId
2. 实现动态路由

升级方式
将下面版本替换

替换前
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-web-starter</artifactId>
    <version>3.1.0-SNAPSHOT</version>
</dependency>
```
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-web-starter</artifactId>
    <version>3.1.0.x-SNAPSHOT</version>
</dependency>
```
替换后
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-dynamic-routing-web-starter</artifactId>
    <version>3.2.0.pa-SNAPSHOT</version>
</dependency>
```
yml添加

```yaml
## 动态代理开关
dynamic:
  enable: true
```

#### 升级后的版本
**yxt-dynamic-routing-starter**：
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-dynamic-routing-starter2</artifactId>
    <version>2.0.0.pa-SNAPSHOT</version>
</dependency>
```
**[grey-pa-spring-boot-gateway-starter](grey-spring-boot-gateway-starter)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-dynamic-routing-gateway-starter</artifactId>
    <version>2.0.0.pa-SNAPSHOT</version>
</dependency>
```

**[grey-pa-spring-boot-web-starter](grey-pa-spring-boot-web-starter)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-dynamic-routing-web-starter</artifactId>
    <version>3.2.0.pa-SNAPSHOT</version>
</dependency>
```
**[grey-pa-spring-boot-lib](grey-pa-spring-boot-lib)**
```xml
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-dynamic-routing-lib</artifactId>
    <version>3.2.0.pa-SNAPSHOT</version>
</dependency>
```
