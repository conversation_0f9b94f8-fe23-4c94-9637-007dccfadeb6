package com.yxt.starter.exception.whitelist;

import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警白名单获取执行器
 *
 * <AUTHOR>
 * @date 2024/01/22 14:08
 **/
@Service
public class YxtErrorLogCodeWhiteListHandler {

    @Resource
    private List<YxtErrorLogCodeWhiteListSupport> whiteList;

    public List<ErrorCodeAlarmWhiteCode> getWhiteList() {
        // 遍历所有 YxtErrorLogCodeWhiteListSupport 实现类，获取白名单
        return whiteList.stream().filter(support -> CollectionUtils.isNotEmpty(support.listErrorCodeAlarmWhiteCode()))
                .flatMap(support -> support.listErrorCodeAlarmWhiteCode().stream())
                .collect(Collectors.toList());
    }

}
