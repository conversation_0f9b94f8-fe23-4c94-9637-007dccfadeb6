package com.yxt.starter.log.oplog;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 该注解用于记录操作日志, 注意：该注解仅限于打在 Controller 的方法上。
 * 例：
 * <pre>
 *  &#64;OpLog(
 *      name = "oms_update_order_status",                               // 必填
 *      label = "OMS更新订单状态",                                        // 必填
 *      content = "修改了订单状态, 将订单号 #orderDto.orderSn 从 #oldStatusName 更改为 #newStatusName",    // 必填
 *      objectId = "#orderDto.orderSn",                                 //选填
 *      tagNames = {"buyerId", "amount"},                               //选填
 *      tagValues = {"#orderDto.buyerId", "#orderDto.amount"}           //选填
 *      )
 *  public ResponseBase updateOrderStatus(@RequestBody OrderDTO orderDto) {
 *      ...
 *
 *      OpLogContext.putVariable("oldStatusName", getStatusName(...));
 *      OpLogContext.putVariable("newStatusName", getStatusName(...));
 *
 *      ...
 *  }
 *  </pre>
 */
@Retention(RetentionPolicy.RUNTIME)
public @interface OpLog {

    /**
     * 必填！
     * 该操作名称, 只能包含英文，数字，下划线，且全局唯一，格式为 {服务名}_{业务模块名}_{操作名}
     */
    String name() default "";

    /**
     * 必填！
     * 该操作名称, 必须是中文
     */
    String label() default "";

    /**
     * 必填！
     * 日志内容，支持使用 Spring Expression 表达式提取变量。
     * 例如: "修改了订单状态, 从 #oldStatusName 更改为 #newStatusName"，变量可以从方法参数中提取，也可以从上下文中提取
     */
    String content() default "";

    /**
     * 选填
     * 操作对象的ID
     */
    String objectId() default "";

    /**
     * 选填
     * 标签名集合，支持使用 Spring Expression 表达式提取变量。
     */
    String[] tagNames() default {};

    /**
     * 选填
     * 标签值集合，支持使用 Spring Expression 表达式提取变量。
     */
    String[] tagValues() default {};

    /**
     * 选填
     * 当前商户, 使用 Spring Expression 表达式提取变量
     */
    String merCode() default "";

    /**
     * 选填, #returnValue 表示返回值
     * 执行结果表达式(表示执行成功或执行失败), 使用 Spring Expression 表达式，结果必须是Boolean类型
     */
    String resultMatchExpr() default "";

}
