<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


  <name>yxt-starter2</name>
  <description>yxt-starter2</description>

  <groupId>com.yxt.starter2</groupId>
  <artifactId>yxt-starter2</artifactId>
  <version>2.0.1-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modelVersion>4.0.0</modelVersion>


  <modules>
    <module>yxt-core-spring-boot-starter2</module>
    <module>yxt-dynamic-routing-starter2</module>
    <module>yxt-mcp-client-starter</module>
    <module>yxt-mcp-server-starter</module>
    <module>yxt-sentinel-spring-boot-starter2</module>
    <module>yxt-xxljob-spring-boot-starter2</module>
  </modules>




  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>

    <repository>
      <id>hydee</id>
      <name>hydee</name>
      <url>http://nexus.hxyxt.com/nexus/content/groups/public/</url>
    </repository>
  </repositories>
  <distributionManagement>
    <repository>
      <id>local-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>local-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>