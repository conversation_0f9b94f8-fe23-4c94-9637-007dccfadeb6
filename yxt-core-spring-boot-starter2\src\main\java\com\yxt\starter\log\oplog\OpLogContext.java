package com.yxt.starter.log.oplog;

import java.util.HashMap;
import java.util.Map;

public class OpLogContext {

    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL = new ThreadLocal<>();

    public static void putVariable(String name, Object value) {
        Map<String, Object> variableMap = THREAD_LOCAL.get();
        if (variableMap == null) {
            variableMap = new HashMap<>();
        }
        variableMap.put(name, value);
        THREAD_LOCAL.set(variableMap);
    }

    static Map<String, Object> getVariables() {
        try {
            Map<String, Object> map = THREAD_LOCAL.get();
            return map == null ? new HashMap<>() : map;
        } finally {
            THREAD_LOCAL.remove();
        }
    }
}
