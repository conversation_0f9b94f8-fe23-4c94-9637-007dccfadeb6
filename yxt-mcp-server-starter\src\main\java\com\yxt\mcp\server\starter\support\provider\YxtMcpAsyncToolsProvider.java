package com.yxt.mcp.server.starter.support.provider;

import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import com.yxt.mcp.server.starter.support.session.YxtSessionFactory;
import com.yxt.mcp.server.starter.support.tool.factory.ToolCallbackFactory;
import io.modelcontextprotocol.server.McpAsyncServer;
import io.modelcontextprotocol.spec.McpServerSession;
import org.springframework.ai.mcp.McpToolUtils;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description Yxt工具提供者
 * @Date 2025/06/12/16:12
 */
public class YxtMcpAsyncToolsProvider implements YxtToolsProvider {

    private final McpAsyncServer mcpAsyncServer;
    private final McpServerSession.Factory sessionFactory;
    private ApplicationContext applicationContext;


    public YxtMcpAsyncToolsProvider(final McpAsyncServer mcpAsyncServer, McpServerSession.Factory sessionFactory,
        ApplicationContext applicationContext) {
        this.mcpAsyncServer = mcpAsyncServer;
        this.sessionFactory = sessionFactory;
        this.applicationContext = applicationContext;
    }

    @Override
    public void addTool(final YxtToolDefinition toolDefinition) {
        try {
            removeTool(toolDefinition.name());
        } catch (Exception e) {
            // Ignore exception
        }
        ToolCallback toolCallback = ToolCallbackFactory.factory(toolDefinition, applicationContext);
        mcpAsyncServer.addTool(McpToolUtils.toAsyncToolSpecification(toolCallback)).block();
        if (sessionFactory instanceof YxtSessionFactory) {
            ((YxtSessionFactory) sessionFactory).initializeHandlers(mcpAsyncServer);
        }
        //通知客户端变更
        mcpAsyncServer.notifyToolsListChanged();
    }

    @Override
    public void removeTool(final String toolName) {
        mcpAsyncServer.removeTool(toolName).block();
        if (sessionFactory instanceof YxtSessionFactory) {
            ((YxtSessionFactory) sessionFactory).initializeHandlers(mcpAsyncServer);
        }
        //通知客户端变更
        mcpAsyncServer.notifyToolsListChanged();
    }
}
