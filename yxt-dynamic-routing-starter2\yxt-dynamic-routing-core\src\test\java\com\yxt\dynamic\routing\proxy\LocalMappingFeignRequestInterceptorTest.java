package com.yxt.dynamic.routing.proxy;

import feign.RequestTemplate;
import feign.Target;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @Description: LocalMappingFeignRequestInterceptor 测试类
 * <AUTHOR>
 * @Date 2024/12/1 10:30
 */
class LocalMappingFeignRequestInterceptorTest {

    private LocalMappingFeignRequestInterceptor interceptor;

    @Mock
    private DynamicProperties dynamicProperties;

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private Target<?> feignTarget;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        interceptor = new LocalMappingFeignRequestInterceptor();
    }

    @Test
    void testApply_WhenDynamicDisabled_ShouldReturn() {
        // Given
        when(dynamicProperties.isEnable()).thenReturn(false);

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).target(anyString());
        }
    }

    @Test
    void testApply_WhenNoLocalMappings_ShouldReturn() {
        // Given
        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(null);

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).target(anyString());
        }
    }

    @Test
    void testApply_WithExactServiceNameMatch_ShouldApplyMapping() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("order-atom-service", "127.0.0.1:8080");

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://order-atom-service");

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).target("http://127.0.0.1:8080");
        }
    }

    @Test
    void testApply_WithRegexMatch_ShouldApplyMapping() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("(.+)", "$1.svc.k8s.test.hxyxt.com");

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://order-service");

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).target("http://order-service.svc.k8s.test.hxyxt.com");
        }
    }

    @Test
    void testApply_WithServiceNameMapping_ShouldUseNacosDiscovery() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("order-atom-service", "order-atom-service-yrk");

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://order-atom-service");

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);
            mockedStatic.when(() -> DynamicBeanComponent.getBean(com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient.class))
                    .thenReturn(null); // 模拟没有NacosDiscoveryClient的情况

            // When
            interceptor.apply(requestTemplate);

            // Then
            // 由于没有NacosDiscoveryClient，应该不会调用target方法
            verify(requestTemplate, never()).target(anyString());
        }
    }

    @Test
    void testApply_WithIPAddress_ShouldNotProcess() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("order-atom-service", "127.0.0.1:8080");

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://*************:8080"); // IP地址，不应该处理

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).target(anyString());
        }
    }

    @Test
    void testApply_WithComplexRegexPattern_ShouldApplyCorrectly() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("order-(.+)-service", "new-$1-service.svc.k8s.test.hxyxt.com");

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://order-atom-service");

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).target("http://new-atom-service.svc.k8s.test.hxyxt.com");
        }
    }

    @Test
    void testApply_WithMultipleMappings_ShouldUseFirstMatch() {
        // Given
        Map<String, String> localMappings = new HashMap<>();
        localMappings.put("order-atom-service", "127.0.0.1:8080"); // 精确匹配
        localMappings.put("(.+)", "$1.svc.k8s.test.hxyxt.com"); // 正则匹配

        when(dynamicProperties.isEnable()).thenReturn(true);
        when(dynamicProperties.getLocalMappings()).thenReturn(localMappings);
        when(requestTemplate.feignTarget()).thenReturn(feignTarget);
        when(feignTarget.url()).thenReturn("http://order-atom-service");

        try (MockedStatic<DynamicBeanComponent> mockedStatic = mockStatic(DynamicBeanComponent.class)) {
            mockedStatic.when(() -> DynamicBeanComponent.getBean(DynamicProperties.class))
                    .thenReturn(dynamicProperties);

            // When
            interceptor.apply(requestTemplate);

            // Then
            // 应该使用精确匹配的结果
            verify(requestTemplate).target("http://127.0.0.1:8080");
        }
    }
}
