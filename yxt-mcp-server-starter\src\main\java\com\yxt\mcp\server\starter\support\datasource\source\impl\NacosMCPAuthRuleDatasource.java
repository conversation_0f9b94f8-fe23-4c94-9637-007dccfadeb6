package com.yxt.mcp.server.starter.support.datasource.source.impl;

import com.alibaba.fastjson2.TypeReference;
import com.yxt.mcp.server.starter.support.datasource.dto.AuthorizationRule;
import com.yxt.mcp.server.starter.support.datasource.listener.McpDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.source.MCPAuthRuleDataSource;
import com.yxt.mcp.server.starter.support.properties.YxtNacosMcpProperties;
import java.util.List;

/**
 * <AUTHOR>
 * @Description nacos存储的鉴权信息
 * @Date 2025/06/16/15:34
 */
public class NacosMCPAuthRuleDatasource extends AbstractNacosDataSource<List<AuthorizationRule>> implements
    MCPAuthRuleDataSource {


    public NacosMCPAuthRuleDatasource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpDataSourceListener> mcpDataSourceListenerList) {
        super(mcpDataSourceListenerList, yxtNacosMcpProperties.getServerAddr(), yxtNacosMcpProperties.getNamespace(),
            yxtNacosMcpProperties.getGroup(),
            yxtNacosMcpProperties.getAuthDataId(), yxtNacosMcpProperties.getTimeout(),
            new TypeReference<List<AuthorizationRule>>() {
            });
    }
}
