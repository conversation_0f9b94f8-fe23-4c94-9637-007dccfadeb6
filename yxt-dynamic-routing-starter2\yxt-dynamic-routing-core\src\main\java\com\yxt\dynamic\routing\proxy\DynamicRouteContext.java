package com.yxt.dynamic.routing.proxy;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 路由信息上下文
 * <AUTHOR>
 * @Date 2024/9/9 14:31
 */
@Slf4j
public class DynamicRouteContext {
    public static final String ROUTE_KEY = "route";
    public static final String ORI_HOST_KEY = "oriHost";

    public static void setOriHost(String ori){
        if(StringUtils.isBlank(getAllRoute())){
            return;
        }
        if(StringUtils.isBlank(ori)){
            return;
        }
        if(ori.startsWith("http")){
            //取host
            try {
                URI uri = new URI(ori);
                ori = uri.getScheme()+"://"+uri.getHost();
                if(uri.getPort()!=-1){
                    ori = ori +":"+uri.getPort();
                }
            } catch (URISyntaxException e) {
                throw new RuntimeException(e);
            }
        }else {
            //服务名称
            ori = "http://"+ori;
        }
        log.info("动态路由 setOriHost thread:{} ori:{}",Thread.currentThread().getName(),ori);
        MDC.put(ORI_HOST_KEY,ori);
    }

    public static void setRoute(String route){
        if(StringUtils.isBlank(route)){
            return;
        }
        log.info("动态路由 setRoute thread:{} route:{}",Thread.currentThread().getName(),route);
        MDC.put(ROUTE_KEY,route);
    }

    public static String getRoute(String appName){
        String allRoute = MDC.get(ROUTE_KEY);
        if(StringUtils.isBlank(allRoute)|| StringUtils.isBlank(appName)){
            return null;
        }
        log.info("动态路由 getRoute thread:{} route:{} appName:{}",Thread.currentThread().getName(),allRoute,appName);
        return getRoute(allRoute,appName);
    }

    public static String getRoute(String routesJson,String appName){
        Map map = JSONObject.parseObject(routesJson, Map.class);
        Object o = map.get(appName);
        if(o==null){
            return null;
        }
        return String.valueOf(o);
    }

    public static String getAllRoute(){
        return MDC.get(ROUTE_KEY);
    }

    public static String getOriHost() {
        return MDC.get(ORI_HOST_KEY);
    }
    public static void clear() {
        String route = MDC.get(ROUTE_KEY);
        String oriHost = MDC.get(ORI_HOST_KEY);
        if(StringUtils.isBlank(route) && StringUtils.isBlank(oriHost)){
            return;
        }
        log.info("动态路由 route clear route:{} , thread:{}",route,Thread.currentThread().getName());
        MDC.clear();
    }

    /**
     * 只支持http方式的匹配
     * @param requestUrl
     * @return
     */
    public static MapEntry<String, String> matchRoute(String requestUrl) {
        String allRoute = getAllRoute();
        if(StringUtils.isBlank(allRoute)|| StringUtils.isBlank(requestUrl)){
            return null;
        }
        if(!requestUrl.startsWith("http") || requestUrl.contains("svc.k8s.test.")){
            return null;
        }
        String serverName = matchServerName(requestUrl);
        //nacos注册的服务不会被修改
        if(StringUtils.isNotBlank(serverName)){
            return null;
        }
        Map<String,String> map = JSONObject.parseObject(allRoute, Map.class);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if(requestUrl.startsWith(entry.getKey())){
                return new MapEntry<>(entry.getKey(),entry.getValue());
            }
        }
        String route = getRoute(map);
        if(StringUtils.isNotBlank(route)){
            try {
                URI oriUri = new URI(requestUrl);
                String s = oriUri.getScheme()+"://"+oriUri.getHost();
                if(oriUri.getPort()!=-1){
                    s = s+":"+oriUri.getPort();
                }
                return new MapEntry<>(s,route);
            } catch (URISyntaxException e) {
                throw new RuntimeException(e);
            }
        }
        log.warn("动态路由 没有匹配上路由 url:{} route:{}",requestUrl,allRoute);
        return null;
    }


    public static String matchServerName(String requestUrl){
        URI uri;
        try {
            uri = new URI(requestUrl);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        String s = uri.getScheme() + "://" + uri.getHost();
        if(uri.getPort()!=-1){
            s = s + ":"+uri.getPort();
        }
        return DynamicRouteContext.getServerName(s);
    }

    private static String getRoute(Map<String, String> route){
        String s = route.get("http://*");
        if(StringUtils.isBlank(s)){
            return route.get("https://*");
        }
        return s;
    }

    private static final ConcurrentHashMap<String,String> serverMapping = new ConcurrentHashMap<>();

    public static String getServerName(String serverIp){
        return serverMapping.get(serverIp);
    }

    public static void setServerName(String serverIp,String serverName){
        serverMapping.put(serverIp,serverName);
    }

}
