package com.yxt.starter.log4j2;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.Marker;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.impl.LocationAwareLogEventFactory;
import org.apache.logging.log4j.core.impl.Log4jLogEvent;
import org.apache.logging.log4j.core.impl.LogEventFactory;
import org.apache.logging.log4j.message.Message;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-11-19 18:21
 * @description:
 */
public class YxtLogEventFactory implements LogEventFactory, LocationAwareLogEventFactory {
    private static final YxtLogEventFactory instance = new YxtLogEventFactory();
    public static boolean useYxtEvent =false;

    public static YxtLogEventFactory getInstance() {
        return instance;
    }
    @Override
    public LogEvent createEvent(
            final String loggerName,
            final Marker marker,
            final String fqcn,
            final Level level,
            final Message data,
            final List<Property> properties,
            final Throwable t) {
        if (useYxtEvent){
            return new YxtLog4jLogEvent(loggerName, marker, fqcn, level, data, properties, t);
        }
        return new Log4jLogEvent(loggerName, marker, fqcn, level, data, properties, t);
    }

    /**
     * Creates a log event.
     *
     * @param loggerName The name of the Logger.
     * @param marker An optional Marker.
     * @param fqcn The fully qualified class name of the caller.
     * @param location The location of the caller
     * @param level The event Level.
     * @param data The Message.
     * @param properties Properties to be added to the log event.
     * @param t An optional Throwable.
     * @return The LogEvent.
     */
    @Override
    public LogEvent createEvent(
            final String loggerName,
            final Marker marker,
            final String fqcn,
            final StackTraceElement location,
            final Level level,
            final Message data,
            final List<Property> properties,
            final Throwable t) {
        if (useYxtEvent){
            return new YxtLog4jLogEvent(loggerName, marker, fqcn, location, level, data, properties, t);
        }
        return new Log4jLogEvent(loggerName, marker, fqcn, level, data, properties, t);
    }
}
