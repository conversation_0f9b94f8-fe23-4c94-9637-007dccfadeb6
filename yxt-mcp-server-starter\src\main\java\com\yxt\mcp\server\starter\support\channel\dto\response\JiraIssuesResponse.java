package com.yxt.mcp.server.starter.support.channel.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * @Description Jira任务列表返回结果
 * @Date 2025/6/11 14:59
 * <AUTHOR>
 */
@Data
public class JiraIssuesResponse {

    //  任务开始位置
    private int startAt;
    //  返回结果数量
    private int maxResults;
    //  任务总数
    private int total;
    // 任务列表
    private List<Issue> issues;


    /**
     * 任务信息
     */
    @Data
    public static class Issue {

        //  任务ID
        private String id;
        //  任务Key
        private String key;
        //  任务信息
        private Fields fields;
    }

    /**
     * @Description 任务信息字段
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class Fields {

        //  任务类型
        private IssueType issuetype;
        //  任务项目
        private Project project;
        //研发PO
        @JsonProperty("customfield_10500")
        private CustomFieldUser customField10500;
        //  任务优先级
        private Priority priority;
        //  经办人
        private CustomFieldUser assignee;
        //  任务状态
        private Status status;
        //  任务描述
        private String description;
        //  测试负责人
        @JsonProperty("customfield_10401")
        private List<CustomFieldUser> customField10401;
        //  任务概要
        private String summary;
        //  任务创建人
        private CustomFieldUser creator;
        //  任务报告人
        private CustomFieldUser reporter;
    }

    /**
     * @Description 任务类型
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class IssueType {

        private String name;
    }

    /**
     * @Description 任务项目
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class Project {

        private String name;
    }

    /**
     * @Description 自定义用户字段
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class CustomFieldUser {

        private String name;
        private String displayName;
    }

    /**
     * @Description 任务优先级
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class Priority {

        private String name;
    }

    /**
     * @Description 任务状态
     * @Date 2025/6/11 15:03
     * <AUTHOR>
     */
    @Data
    public static class Status {

        private String name;
    }
}

