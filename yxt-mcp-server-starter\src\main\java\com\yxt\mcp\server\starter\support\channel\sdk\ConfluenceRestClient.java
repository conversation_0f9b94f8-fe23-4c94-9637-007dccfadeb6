package com.yxt.mcp.server.starter.support.channel.sdk;

import com.yxt.mcp.server.starter.support.channel.dto.response.ConfluenceSearchResponse;
import com.yxt.mcp.server.starter.support.channel.properties.MCPToolChannelProperties;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * cf渠道
 */
@Component
public class ConfluenceRestClient implements InitializingBean {

    private RestClient restClient;

    @Resource
    private MCPToolChannelProperties mcpToolChannelProperties;


    /**
     * Search Confluence content with a query string and optional time range
     *
     * @param query     The search query string
     * @param timeRange Optional time range for filtering by last modified date (e.g., "-1d" for last day)
     * @return ConfluenceSearchResponse containing search results
     */
    public ConfluenceSearchResponse searchList(String query, String timeRange) {
        StringBuilder cqlBuilder = new StringBuilder();
        cqlBuilder.append(String.format("siteSearch ~ \"%s\" AND type in (\"blogpost\",\"page\")", query));

        // Add time range filter if provided
        if (timeRange != null && !timeRange.isEmpty()) {
            cqlBuilder.append(String.format(" AND lastmodified >= now(\"%s\")", timeRange));
        }

        String url = UriComponentsBuilder.fromPath("/rest/api/search")
            .queryParam("cql", cqlBuilder.toString())
            .queryParam("start", "0")
            .queryParam("limit", "5")
            .queryParam("expand", "space.icon")
            .queryParam("includeArchivedSpaces", "false")
            .queryParam("src", "next.ui.search")
            .build()
            .toUriString();
        ConfluenceSearchResponse body = restClient.get()
            .uri(url)
            .retrieve()
            .body(ConfluenceSearchResponse.class);
        List<ConfluenceSearchResponse.SearchResult> results =
            body.getResults();
        results.stream()
            .map(new Function<ConfluenceSearchResponse.SearchResult, ConfluenceSearchResponse.SearchResult>() {
                @Override
                public ConfluenceSearchResponse.SearchResult apply(ConfluenceSearchResponse.SearchResult r) {
                    r.setExcerpt(r.getExcerpt().replaceAll("@@@hl@@@", "").replaceAll("@@@endhl@@@", ""));
                    return r;
                }
            }).collect(Collectors.toList());
        return body;
    }

    /**
     * Search Confluence content with a query string using default settings (no time range filter)
     *
     * @param query The search query string
     * @return ConfluenceSearchResponse containing search results
     */
    public ConfluenceSearchResponse searchList(String query) {
        return searchList(query, null);
    }


    /**
     * Get a specific Confluence page by its pageId
     *
     * @param pageId the ID of the page to retrieve
     * @return the page content as a String
     */
    public String getPageById(String pageId) {
        String url = UriComponentsBuilder.fromPath("/pages/viewpage.action")
            .queryParam("pageId", pageId)
            .build()
            .toUriString();
        return getByUrl(url);
    }

    public String getByUrl(String url) {
        return restClient.get()
            .uri(url)
            .retrieve()
            .body(String.class);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String confluenceUrl = mcpToolChannelProperties.getConfluence().getUrl();
        String personalToken = mcpToolChannelProperties.getConfluence().getToken();
        this.restClient = RestClient.builder()
            .baseUrl(confluenceUrl)
            .defaultHeader("Authorization", "Bearer " + personalToken)
            .defaultHeader("Content-Type", "application/json")
            .build();
    }
}