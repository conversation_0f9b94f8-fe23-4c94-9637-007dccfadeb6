package com.yxt.starter.sentinel.transtport.heartbeat;

import com.alibaba.csp.sentinel.transport.heartbeat.HeartbeatMessage;
import com.alibaba.fastjson2.JSON;
import com.yxt.starter.sentinel.YxtSentinelProperties;
import com.yxt.starter.sentinel.YxtSentinelProperties.YxtAuth;
import com.yxt.starter.sentinel.constants.YxtSentinelConstants;
import com.yxt.starter.sentinel.spring.SpringContextUtil;
import java.util.Arrays;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;

/**
 * @Author: qiang.li
 * @Date: 2024/9/29 11:17
 * @Description: 与dashboard通信的message
 */
public class YxtHeartbeatMessage extends HeartbeatMessage {

    private static final Logger logger = LoggerFactory.getLogger(
        YxtHeartbeatMessage.class);

    @Override
    public Map<String, String> generateCurrentMessage() {
        Map<String, String> heartbeatMessage = super.generateCurrentMessage();
        //增加auth配置
        setYxtAuthConfig(heartbeatMessage);
        return heartbeatMessage;
    }

    /**
     * 设置授权相关配置
     */
    public void setYxtAuthConfig(Map<String, String> heartbeatMessage) {
        ApplicationContext applicationContext = SpringContextUtil.getApplicationContext();
        if (applicationContext == null) {
            logger.warn("#31 YxtHeartbeatMessage setYxtAuthConfig fail applicationContext is null");
            return;
        }
        YxtSentinelProperties yxtSentinelProperties = applicationContext.getBean(YxtSentinelProperties.class);
        if (logger.isDebugEnabled()) {
            logger.debug("#46 setYxtAuthConfig yxtSentinelProperties.getAuth() == null={}",
                yxtSentinelProperties.getAuth() == null);
        }
        //未做定制化auth配置兼容告警配置
        if (yxtSentinelProperties.getAuth() == null) {
            setAlarmConfigConvertYxtAuth(heartbeatMessage, applicationContext);
            return;
        }
        //上报授权配置
        heartbeatMessage.put(YxtSentinelConstants.AUTH_FIELD_NAME, JSON.toJSONString(yxtSentinelProperties.getAuth()));


    }

    private void setAlarmConfigConvertYxtAuth(Map<String, String> heartbeatMessage,
        ApplicationContext applicationContext) {
        Environment environment = applicationContext.getEnvironment();
        String tagsStr = environment.getProperty(YxtSentinelConstants.ALARM_TAG_KEY);
        String toUserStr = environment.getProperty(YxtSentinelConstants.ALARM_USER_KEY);
        if (logger.isDebugEnabled()) {
            logger.debug("#setAlarmConfigConvertYxtAuth tagsStr={},toUserStr={}", tagsStr, toUserStr);
        }
        if (StringUtils.isBlank(tagsStr) && StringUtils.isBlank(toUserStr)) {
            return;
        }
        YxtAuth auth = new YxtAuth();
        if (StringUtils.isNotBlank(tagsStr)) {
            String[] tagArr = tagsStr.split(YxtSentinelConstants.SPLITTER);
            auth.setWechatTagList(Arrays.asList(tagArr));
        }
        if (StringUtils.isNotBlank(toUserStr)) {
            String[] userArr = toUserStr.split(YxtSentinelConstants.SPLITTER);
            auth.setUserIdList(Arrays.asList(userArr));
        }
        //上报授权配置
        heartbeatMessage.put(YxtSentinelConstants.AUTH_FIELD_NAME, JSON.toJSONString(auth));

    }
}
