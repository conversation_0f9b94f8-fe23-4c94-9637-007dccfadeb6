--redis.log(redis.LOG_NOTICE, "start script!!!!!!!!")
local key = KEYS[1];
local limit = tonumber(ARGV[1]);
local interval = tonumber(ARGV[2]);
local stepNum = tonumber(ARGV[3]);
local currentTimeMills = tonumber(ARGV[4]);
local expiredSeconds = tonumber(ARGV[5]);
local lastClearTimeKey = KEYS[2];
--redis.log(redis.LOG_NOTICE, "key:" .. key)
--redis.log(redis.LOG_NOTICE, "limit:" .. limit)
--redis.log(redis.LOG_NOTICE, "interval:" .. interval)
--redis.log(redis.LOG_NOTICE, "stepNum:" .. stepNum)
--redis.log(redis.LOG_NOTICE, "currentTimeMills:" .. currentTimeMills)
--redis.log(redis.LOG_NOTICE, "expiredSeconds:" .. expiredSeconds)
--redis.log(redis.LOG_NOTICE, "lastClearTimeKey:" .. lastClearTimeKey)

local hasKey = redis.call('EXISTS', key);
local hasLastClearTimeKey = redis.call('EXISTS', lastClearTimeKey);
--redis.log(redis.LOG_NOTICE, "hasKey:" .. hasKey)
--redis.log(redis.LOG_NOTICE, "hasLastClearTimeKey:" .. hasLastClearTimeKey)

if hasKey == 1 and hasLastClearTimeKey == 1 then
    local lastClearTime = redis.call('GET', lastClearTimeKey);
    local diff = tonumber(currentTimeMills) - tonumber(lastClearTime);
--    redis.log(redis.LOG_NOTICE, "diff:" .. diff)
    local value = tonumber(redis.call('GET', key));
--    redis.log(redis.LOG_NOTICE, "value:" .. value)
    if diff >= interval then
        local maxValue = value + math.ceil(diff / interval) * stepNum;
--        redis.log(redis.LOG_NOTICE, "maxValue:" .. maxValue)
        if maxValue > limit then
            value = limit;
        else
            value = maxValue;
        end
--        redis.log(redis.LOG_NOTICE, "value:" .. value)
        redis.call('SET', lastClearTimeKey, currentTimeMills);
        redis.call('SET', key, value);
    end
    if value <= 0 then
--        redis.log(redis.LOG_NOTICE, "return -1")
        redis.call('expire', key, expiredSeconds);
        redis.call('expire', lastClearTimeKey, expiredSeconds);
        return -1;
    else
        redis.call('DECR', key);
    end
else
    redis.call('SET', key, limit - 1);
    redis.call('SET', lastClearTimeKey, currentTimeMills);
end
redis.call('expire', key, expiredSeconds);
redis.call('expire', lastClearTimeKey, expiredSeconds);
return 1;