package com.yxt.starter.controller;

import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindingResult;

/**
 * 控制器抽象基类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/24 10:43
 */
@Data
public abstract class BaseController {
    @Value("${api.version:1.0}")
    private String API_VERSION;

    @Value("${api.base-info-version:1.0}")
    private String API_BASE_INFO_VERSION;

    protected void checkValid(BindingResult result) {
        if (result.hasErrors()) {
            String message = result.getFieldError().getDefaultMessage();
            throw new YxtBizException(message);
        }
    }

    protected <T> ResponseBase<T> generateSuccess(T tObject) {
        ResponseBase<T> base = ResponseBase.success();
        base.setData(tObject);
        return base;
    }

    protected <T> ResponseBase<T> generateObjectSuccess(T tObject) {
        ResponseBase<T> base = ResponseBase.success();
        base.setData(tObject);
        return base;
    }

    /**
     * 根据对数据库影响的行数，统一返回操作成功和失败
     * <AUTHOR>
     * @date 11:20 2020/04/10
     * @param res 数据库影响行数
     * @return com.yxt.starter.dto.ResponseBase
     **/
    protected ResponseBase generateBitSuccess(boolean res) {
        if (res) {
            return ResponseBase.success();
        } else {
            return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
        }
    }
    /**
     * 根据对数据库影响的行数，统一返回操作成功和失败
     * <AUTHOR>
     * @date 11:20 2019/11/29
     * @param line 数据库影响行数
     * @param size 目标数，为0，则line>0表示成功，不为0，line==size表示成功
     * @return com.yxt.starter.dto.ResponseBase
     **/
    protected ResponseBase generateLineSuccess(int line,int size) {
        if (size == 0) {
            if (line > size) {
                return ResponseBase.success();
            } else {
                return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
            }
        } else {
            if (line == size) {
                return ResponseBase.success();
            } else {
                return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
            }
        }
    }

    protected <T> ResponseBase<PageDTO<T>> generatePageDtoSuccess(Long total,List<T> list) {
        ResponseBase<PageDTO<T>> base = ResponseBase.success();
        PageDTO<T> pageDTO = new PageDTO<T>();
        pageDTO.setTotalCount(total);
        pageDTO.setData(list);
        base.setData(pageDTO);
        return base;
    }

    protected <T> ResponseBase<T> generateError(ResponseCodeType type) {
        return ResponseBase.fail(type);
    }

}
