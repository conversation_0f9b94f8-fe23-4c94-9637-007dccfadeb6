# yxt-core-spring-boot-starter

-------------------------------------------------------------------------------

## 📚简介

`yxt-core-spring-boot-starter`作为yxt核心starter，Java项目基础Starter，包含基础Configuration、Filter等。详见：[yxt-spring-boot-starter](https://yxtcf.hxyxt.com/display/brd/1.+yxt-spring-boot-starter)。

## 🛠️包含能力
### 服务元数据
### 集成Swagger文档
### 灰度发布，平滑升级，优雅停机
### 全局异常处理器
### 统一HTTP响应对象及公共异常码
### 集成Skywalking链路ID
### 提供部分工具类
### retryTemplate重试功能
## 📝chang log

### 4.0.0

项目迁移初始化
>1. 项目迁移初始化
>2. 修改retryTemplate加载规则，使retryTemplate可动态调整配置
