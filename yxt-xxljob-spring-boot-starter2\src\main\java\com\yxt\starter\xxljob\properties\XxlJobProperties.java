package com.yxt.starter.xxljob.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@ConfigurationProperties(prefix = "xxl.job")
public class XxlJobProperties {
    /**
     * 执行器通讯TOKEN [选填]：非空时启用；
     */
    private String accessToken = "sk_token";
    /**
     * 调度中心相关配置
     */
    private XxlAdminProperties admin = new XxlAdminProperties();
    /**
     * 执行器相关配置
     */
    private XxlExecutorProperties executor = new XxlExecutorProperties();

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public XxlAdminProperties getAdmin() {
        return admin;
    }

    public void setAdmin(XxlAdminProperties admin) {
        this.admin = admin;
    }

    public XxlExecutorProperties getExecutor() {
        return executor;
    }

    public void setExecutor(XxlExecutorProperties executor) {
        this.executor = executor;
    }
}