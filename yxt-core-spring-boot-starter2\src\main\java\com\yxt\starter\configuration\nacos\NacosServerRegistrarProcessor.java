package com.yxt.starter.configuration.nacos;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.List;

/**
 * 本地Window环境不注册服务到测试环境处理器
 *
 * <AUTHOR>
 * @date 2024/6/18 18:33
 */
public class NacosServerRegistrarProcessor implements BeanPostProcessor {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 本地、开发、测试环境
     */
    private final List<String> FORBID_ENV = Arrays.asList("local", "dev", "test");

    private final String WINDOWS = "windows";

    private final String MAC = "mac";

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof NacosDiscoveryProperties){
            if ("true".equals(applicationContext.getEnvironment().getProperty("middleware.register.nacosSwitch"))) {
                return bean;
            }
            // 获取环境
            String env = applicationContext.getEnvironment().getActiveProfiles()[0];
            // 获取操作系统名称
            String osName = System.getProperty("os.name").toLowerCase();
            // window、mac环境服务不注册到nacos
            if(isForbidEnv(env) && (isWindows(osName) || isMac(osName))){
                ((NacosDiscoveryProperties) bean).setRegisterEnabled(false);
            }
        }
        return bean;
    }

    private boolean isWindows(String osName) {
        return StringUtils.isNotBlank(osName) && osName.contains(WINDOWS);
    }

    private boolean isMac(String osName) {
        return StringUtils.isNotBlank(osName) && osName.contains(MAC);
    }

    private boolean isForbidEnv(String env) {
        String lowerCaseEnv = StringUtils.lowerCase(env);
        for (String forbidEnv : FORBID_ENV) {
            if (lowerCaseEnv.contains(forbidEnv)) {
                return true;
            }
        }
        return false;
    }

}
