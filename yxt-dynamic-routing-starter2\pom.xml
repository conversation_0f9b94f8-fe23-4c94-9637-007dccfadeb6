<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-xframe2</artifactId>
    <version>2.0.1-SNAPSHOT</version>
  </parent>

  <name>yxt-dynamic-routing-starter2</name>
  <description>yxt-dynamic-routing-starter2</description>

  <groupId>com.yxt.dynamic.routing</groupId>
  <artifactId>yxt-dynamic-routing-starter2</artifactId>
  <version>2.0.1-dev2-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modelVersion>4.0.0</modelVersion>

  <properties>
    <dynamic.routing>2.0.1-SNAPSHOT</dynamic.routing>

    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <module.deploy.skip>false</module.deploy.skip>
  </properties>

  <modules>
    <module>yxt-dynamic-routing-agent</module>
    <module>yxt-dynamic-routing-core</module>
    <module>yxt-dynamic-routing-web-starter</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>




  <repositories>
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>

    <repository>
      <id>hydee</id>
      <name>hydee</name>
      <url>http://nexus.hxyxt.com/nexus/content/groups/public/</url>
    </repository>
  </repositories>
  <distributionManagement>
    <repository>
      <id>local-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.hxyxt.com/repository/releases/</url>
    </repository>
    <snapshotRepository>
      <id>local-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.hxyxt.com/repository/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

</project>