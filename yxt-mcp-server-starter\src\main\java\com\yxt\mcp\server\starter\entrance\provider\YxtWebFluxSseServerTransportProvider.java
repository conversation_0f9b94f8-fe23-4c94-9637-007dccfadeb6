package com.yxt.mcp.server.starter.entrance.provider;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yxt.lang.exception.YxtBaseException;
import com.yxt.mcp.server.starter.auth.AuthorizationService;
import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.support.constants.AuthFieldConstants;
import io.modelcontextprotocol.server.transport.WebFluxSseServerTransportProvider;
import io.modelcontextprotocol.spec.McpError;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpServerSession;
import io.modelcontextprotocol.spec.McpServerTransport;
import io.modelcontextprotocol.spec.McpServerTransportProvider;
import io.modelcontextprotocol.util.Assert;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.Exceptions;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;

/**
 * 参考WebFluxSseServerTransportProvider 实现，只是重写了handleSseConnection
 */
public class YxtWebFluxSseServerTransportProvider implements McpServerTransportProvider {

    /**
     * Event type for JSON-RPC messages sent through the SSE connection.
     */
    public static final String MESSAGE_EVENT_TYPE = "message";
    /**
     * Event type for sending the message endpoint URI to clients.
     */
    public static final String ENDPOINT_EVENT_TYPE = "endpoint";
    /**
     * Default SSE endpoint path as specified by the MCP transport specification.
     */
    public static final String DEFAULT_SSE_ENDPOINT = "/sse";
    public static final String DEFAULT_BASE_URL = "";
    private static final Logger logger = LoggerFactory.getLogger(
        WebFluxSseServerTransportProvider.class);
    private final ObjectMapper objectMapper;

    /**
     * Base URL for the message endpoint. This is used to construct the full URL for clients to send their JSON-RPC
     * messages.
     */
    private final String baseUrl;

    private final String messageEndpoint;

    private final String sseEndpoint;

    private final RouterFunction<?> routerFunction;

    private final AuthorizationService authorizationService;
    /**
     * Map of active client sessions, keyed by session ID.
     */
    private final ConcurrentHashMap<String, McpServerSession> sessions = new ConcurrentHashMap<>();
    private McpServerSession.Factory sessionFactory;
    /**
     * Flag indicating if the transport is shutting down.
     */
    private volatile boolean isClosing = false;

    /**
     * Constructs a new WebFlux SSE server transport provider instance with the default SSE endpoint.
     *
     * @param objectMapper    The ObjectMapper to use for JSON serialization/deserialization of MCP messages. Must not
     *                        be null.
     * @param messageEndpoint The endpoint URI where clients should send their JSON-RPC messages. This endpoint will be
     *                        communicated to clients during SSE connection setup. Must not be null.
     * @throws IllegalArgumentException if either parameter is null
     */
    public YxtWebFluxSseServerTransportProvider(ObjectMapper objectMapper, String messageEndpoint,
        AuthorizationService authorizationService) {
        this(objectMapper, messageEndpoint, DEFAULT_SSE_ENDPOINT, authorizationService);
    }

    /**
     * Constructs a new WebFlux SSE server transport provider instance.
     *
     * @param objectMapper    The ObjectMapper to use for JSON serialization/deserialization of MCP messages. Must not
     *                        be null.
     * @param messageEndpoint The endpoint URI where clients should send their JSON-RPC messages. This endpoint will be
     *                        communicated to clients during SSE connection setup. Must not be null.
     * @throws IllegalArgumentException if either parameter is null
     */
    public YxtWebFluxSseServerTransportProvider(ObjectMapper objectMapper, String messageEndpoint, String sseEndpoint,
        AuthorizationService authorizationService) {
        this(objectMapper, DEFAULT_BASE_URL, messageEndpoint, sseEndpoint, authorizationService);
    }

    /**
     * Constructs a new WebFlux SSE server transport provider instance.
     *
     * @param objectMapper    The ObjectMapper to use for JSON serialization/deserialization of MCP messages. Must not
     *                        be null.
     * @param baseUrl         webflux message base path
     * @param messageEndpoint The endpoint URI where clients should send their JSON-RPC messages. This endpoint will be
     *                        communicated to clients during SSE connection setup. Must not be null.
     * @throws IllegalArgumentException if either parameter is null
     */
    public YxtWebFluxSseServerTransportProvider(ObjectMapper objectMapper, String baseUrl, String messageEndpoint,
        String sseEndpoint, AuthorizationService authorizationService) {
        Assert.notNull(objectMapper, "ObjectMapper must not be null");
        Assert.notNull(baseUrl, "Message base path must not be null");
        Assert.notNull(messageEndpoint, "Message endpoint must not be null");
        Assert.notNull(sseEndpoint, "SSE endpoint must not be null");
        this.authorizationService = authorizationService;
        this.objectMapper = objectMapper;
        this.baseUrl = baseUrl;
        this.messageEndpoint = messageEndpoint;
        this.sseEndpoint = sseEndpoint;
        this.routerFunction = RouterFunctions.route()
            .GET(this.sseEndpoint, this::handleSseConnection)
            .POST(this.messageEndpoint, this::handleMessage)
            .build();
    }

    public static WebFluxSseServerTransportProvider.Builder builder() {
        return new WebFluxSseServerTransportProvider.Builder();
    }

    @Override
    public void setSessionFactory(McpServerSession.Factory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }

    // FIXME: This javadoc makes claims about using isClosing flag but it's not
    // actually
    // doing that.

    /**
     * Broadcasts a JSON-RPC message to all connected clients through their SSE connections. The message is serialized
     * to JSON and sent as a server-sent event to each active session.
     *
     * <p>
     * The method:
     * <ul>
     * <li>Serializes the message to JSON</li>
     * <li>Creates a server-sent event with the message data</li>
     * <li>Attempts to send the event to all active sessions</li>
     * <li>Tracks and reports any delivery failures</li>
     * </ul>
     *
     * @param method The JSON-RPC method to send to clients
     * @param params The method parameters to send to clients
     * @return A Mono that completes when the message has been sent to all sessions, or errors if any session fails to
     * receive the message
     */
    @Override
    public Mono<Void> notifyClients(String method, Object params) {
        if (sessions.isEmpty()) {
            logger.debug("No active sessions to broadcast message to");
            return Mono.empty();
        }

        logger.debug("Attempting to broadcast message to {} active sessions", sessions.size());

        return Flux.fromIterable(sessions.values())
            .flatMap(session -> session.sendNotification(method, params)
                .doOnError(
                    e -> logger.error("Failed to send message to session {}: {}", session.getId(), e.getMessage()))
                .onErrorComplete())
            .then();
    }

    /**
     * Initiates a graceful shutdown of all the sessions. This method ensures all active sessions are properly closed
     * and cleaned up.
     *
     * <p>
     * The shutdown process:
     * <ul>
     * <li>Marks the transport as closing to prevent new connections</li>
     * <li>Closes each active session</li>
     * <li>Removes closed sessions from the sessions map</li>
     * <li>Times out after 5 seconds if shutdown takes too long</li>
     * </ul>
     *
     * @return A Mono that completes when all sessions have been closed
     */
    @Override
    public Mono<Void> closeGracefully() {
        return Flux.fromIterable(sessions.values())
            .doFirst(() -> logger.debug("Initiating graceful shutdown with {} active sessions", sessions.size()))
            .flatMap(McpServerSession::closeGracefully)
            .then();
    }

    /**
     * Returns the WebFlux router function that defines the transport's HTTP endpoints. This router function should be
     * integrated into the application's web configuration.
     *
     * <p>
     * The router function defines two endpoints:
     * <ul>
     * <li>GET {sseEndpoint} - For establishing SSE connections</li>
     * <li>POST {messageEndpoint} - For receiving client messages</li>
     * </ul>
     *
     * @return The configured {@link RouterFunction} for handling HTTP requests
     */
    public RouterFunction<?> getRouterFunction() {
        return this.routerFunction;
    }

    /**
     * Handles new SSE connection requests from clients. Creates a new session for each connection and sets up the SSE
     * event stream.
     *
     * @param request The incoming server request
     * @return A Mono which emits a response with the SSE event stream
     */
    private Mono<ServerResponse> handleSseConnection(ServerRequest request) {
        if (isClosing) {
            return ServerResponse.status(HttpStatus.SERVICE_UNAVAILABLE).bodyValue("Server is shutting down");
        }
        // 提取请求参数
        Map<String, List<String>> queryParams = request.exchange().getRequest().getQueryParams();
        List<String> userIdList = queryParams.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID);
        List<String> empCodeList = queryParams.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID);
        List<String> appIdList = queryParams.get(AuthFieldConstants.APP_ID);
        List<String> businessScenarioList = queryParams.get(
            AuthFieldConstants.BUSINESS_SCENARIO);
        List<String> tokenList = queryParams.get(
            AuthFieldConstants.TOKEN);

        String userId = userIdList != null && !userIdList.isEmpty() ? userIdList.get(0) : null;
        String empCode = empCodeList != null && !empCodeList.isEmpty() ? empCodeList.get(0) : null;
        String appId = appIdList != null && !appIdList.isEmpty() ? appIdList.get(0) : null;
        String businessScenario =
            businessScenarioList != null && !businessScenarioList.isEmpty() ? businessScenarioList.get(0) : null;
        String token = tokenList != null && !tokenList.isEmpty() ? tokenList.get(0) : null;
        YxtAuthResult yxtAuthResult = null;
        try {
            //liqiangtodo SSE链接鉴权
            Optional<YxtAuthResult> yxtAuthResultOpt = authorizationService.parseYxtAuthResult(appId, businessScenario,
                userId, empCode, token);
            yxtAuthResult = yxtAuthResultOpt.orElse(null);
            if (!authorizationService.checkSSEPermission(appId, businessScenario, yxtAuthResult)) {
                logger.warn("鉴权失败userId={},appId={},businessScenario={},token={}", userId, appId,
                    businessScenario, token);
                return ServerResponse.status(HttpStatus.FORBIDDEN).bodyValue("Access denied");
            }
        } catch (YxtBaseException e) {
            logger.warn("鉴权失败userId={},appId={},businessScenario={},token={},remark={}", userId, appId,
                businessScenario,
                token, e.getMessage());
            return ServerResponse.status(HttpStatus.FORBIDDEN).bodyValue(e.getMessage());
        } catch (Exception e) {
            logger.error("鉴权异常userId={},appId={},businessScenario={},token={}", userId, appId, businessScenario,
                token, e);
            return ServerResponse.status(HttpStatus.FORBIDDEN).bodyValue("Access denied");
        }
        final YxtAuthResult finalAuthResult = yxtAuthResult;
        return ServerResponse.ok()
            .contentType(MediaType.TEXT_EVENT_STREAM)
            .body(Flux.<ServerSentEvent<?>>create(sink -> {
                WebFluxMcpSessionTransport sessionTransport = new WebFluxMcpSessionTransport(finalAuthResult, appId,
                    businessScenario,
                    sink);

                McpServerSession session = sessionFactory.create(sessionTransport);
                String sessionId = session.getId();

                logger.debug("Created new SSE connection for session: {}", sessionId);
                sessions.put(sessionId, session);

                // Send initial endpoint event
                logger.debug("Sending initial endpoint event to session: {}", sessionId);
                sink.next(ServerSentEvent.builder()
                    .event(ENDPOINT_EVENT_TYPE)
                    .data(this.baseUrl + this.messageEndpoint + "?sessionId=" + sessionId)
                    .build());
                sink.onCancel(() -> {
                    logger.debug("Session {} cancelled", sessionId);
                    sessions.remove(sessionId);
                });
            }), ServerSentEvent.class);
    }

    /**
     * Handles incoming JSON-RPC messages from clients. Deserializes the message and processes it through the configured
     * message handler.
     *
     * <p>
     * The handler:
     * <ul>
     * <li>Deserializes the incoming JSON-RPC message</li>
     * <li>Passes it through the message handler chain</li>
     * <li>Returns appropriate HTTP responses based on processing results</li>
     * <li>Handles various error conditions with appropriate error responses</li>
     * </ul>
     *
     * @param request The incoming server request containing the JSON-RPC message
     * @return A Mono emitting the response indicating the message processing result
     */
    private Mono<ServerResponse> handleMessage(ServerRequest request) {
        if (isClosing) {
            return ServerResponse.status(HttpStatus.SERVICE_UNAVAILABLE).bodyValue("Server is shutting down");
        }

        if (request.queryParam("sessionId").isEmpty()) {
            return ServerResponse.badRequest().bodyValue(new McpError("Session ID missing in message endpoint"));
        }

        McpServerSession session = sessions.get(request.queryParam("sessionId").get());

        if (session == null) {
            return ServerResponse.status(HttpStatus.NOT_FOUND)
                .bodyValue(new McpError("Session not found: " + request.queryParam("sessionId").get()));
        }

        return request.bodyToMono(String.class).flatMap(body -> {
            try {
                McpSchema.JSONRPCMessage message = McpSchema.deserializeJsonRpcMessage(objectMapper, body);
                return session.handle(message).flatMap(response -> ServerResponse.ok().build()).onErrorResume(error -> {
                    logger.error("Error processing  message: {}", error.getMessage());
                    // TODO: instead of signalling the error, just respond with 200 OK
                    // - the error is signalled on the SSE connection
                    // return ServerResponse.ok().build();
                    return ServerResponse.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .bodyValue(new McpError(error.getMessage()));
                });
            } catch (IllegalArgumentException | IOException e) {
                logger.error("Failed to deserialize message: {}", e.getMessage());
                return ServerResponse.badRequest().bodyValue(new McpError("Invalid message format"));
            }
        });
    }

    /**
     * Builder for creating instances of {@link WebFluxSseServerTransportProvider}.
     * <p>
     * This builder provides a fluent API for configuring and creating instances of WebFluxSseServerTransportProvider
     * with custom settings.
     */
    public static class Builder {

        private ObjectMapper objectMapper;

        private String baseUrl = DEFAULT_BASE_URL;

        private String messageEndpoint;

        private String sseEndpoint = DEFAULT_SSE_ENDPOINT;

        /**
         * Sets the ObjectMapper to use for JSON serialization/deserialization of MCP messages.
         *
         * @param objectMapper The ObjectMapper instance. Must not be null.
         * @return this builder instance
         * @throws IllegalArgumentException if objectMapper is null
         */
        public Builder objectMapper(
            ObjectMapper objectMapper) {
            Assert.notNull(objectMapper, "ObjectMapper must not be null");
            this.objectMapper = objectMapper;
            return this;
        }

        /**
         * Sets the project basePath as endpoint prefix where clients should send their JSON-RPC messages
         *
         * @param baseUrl the message basePath . Must not be null.
         * @return this builder instance
         * @throws IllegalArgumentException if basePath is null
         */
        public Builder basePath(
            String baseUrl) {
            Assert.notNull(baseUrl, "basePath must not be null");
            this.baseUrl = baseUrl;
            return this;
        }

        /**
         * Sets the endpoint URI where clients should send their JSON-RPC messages.
         *
         * @param messageEndpoint The message endpoint URI. Must not be null.
         * @return this builder instance
         * @throws IllegalArgumentException if messageEndpoint is null
         */
        public Builder messageEndpoint(
            String messageEndpoint) {
            Assert.notNull(messageEndpoint, "Message endpoint must not be null");
            this.messageEndpoint = messageEndpoint;
            return this;
        }

        /**
         * Sets the SSE endpoint path.
         *
         * @param sseEndpoint The SSE endpoint path. Must not be null.
         * @return this builder instance
         * @throws IllegalArgumentException if sseEndpoint is null
         */
        public Builder sseEndpoint(
            String sseEndpoint) {
            Assert.notNull(sseEndpoint, "SSE endpoint must not be null");
            this.sseEndpoint = sseEndpoint;
            return this;
        }

        /**
         * Builds a new instance of {@link WebFluxSseServerTransportProvider} with the configured settings.
         *
         * @return A new WebFluxSseServerTransportProvider instance
         * @throws IllegalStateException if required parameters are not set
         */
        public WebFluxSseServerTransportProvider build() {
            Assert.notNull(objectMapper, "ObjectMapper must be set");
            Assert.notNull(messageEndpoint, "Message endpoint must be set");

            return new WebFluxSseServerTransportProvider(objectMapper, baseUrl,
                messageEndpoint, sseEndpoint);
        }

    }

    public class WebFluxMcpSessionTransport implements McpServerTransport {

        private final FluxSink<ServerSentEvent<?>> sink;

        /**
         * 用户信息
         */
        private YxtAuthResult yxtAuthResult;

        /**
         * appId
         */
        private String appId;
        /**
         * 业务场景
         */
        private String businessScenario;

        public WebFluxMcpSessionTransport(YxtAuthResult yxtAuthResult, String appId, String businessScenario,
            FluxSink<ServerSentEvent<?>> sink) {
            this.sink = sink;
            this.yxtAuthResult = yxtAuthResult;
            this.appId = appId;
            this.businessScenario = businessScenario;
        }

        @Override
        public Mono<Void> sendMessage(McpSchema.JSONRPCMessage message) {
            return Mono.fromSupplier(() -> {
                try {
                    return objectMapper.writeValueAsString(message);
                } catch (IOException e) {
                    throw Exceptions.propagate(e);
                }
            }).doOnNext(jsonText -> {
                ServerSentEvent<Object> event = ServerSentEvent.builder()
                    .event(MESSAGE_EVENT_TYPE)
                    .data(jsonText)
                    .build();
                sink.next(event);
            }).doOnError(e -> {
                // TODO log with sessionid
                Throwable exception = Exceptions.unwrap(e);
                sink.error(exception);
            }).then();
        }

        @Override
        public <T> T unmarshalFrom(Object data, TypeReference<T> typeRef) {
            return objectMapper.convertValue(data, typeRef);
        }

        @Override
        public Mono<Void> closeGracefully() {
            return Mono.fromRunnable(sink::complete);
        }

        @Override
        public void close() {
            sink.complete();
        }

        public YxtAuthResult getYxtAuthResult() {
            return yxtAuthResult;
        }

        public void setYxtAuthResult(YxtAuthResult yxtAuthResult) {
            this.yxtAuthResult = yxtAuthResult;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getBusinessScenario() {
            return businessScenario;
        }

        public void setBusinessScenario(String businessScenario) {
            this.businessScenario = businessScenario;
        }
    }

}
