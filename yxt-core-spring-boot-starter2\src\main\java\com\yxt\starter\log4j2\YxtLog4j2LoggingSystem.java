package com.yxt.starter.log4j2;

import com.yxt.common.alarm.util.ApolloConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.ThreadContext;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Configurator;
import org.apache.logging.log4j.core.config.xml.XmlConfiguration;
import org.springframework.boot.logging.LogFile;
import org.springframework.boot.logging.LoggingInitializationContext;
import org.springframework.boot.logging.log4j2.Log4J2LoggingSystem;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.net.URL;
import java.util.List;

import static com.yxt.common.alarm.util.AlarmConst.CONF_APPLICATION_NAME;


/**
 * <p><b>自定义 {@link Log4J2LoggingSystem}</b></p>
 *
 * <p>提供以下能力：</p>
 * <ol>
 *     <li>当 Spring 配置文件中没有指定 {@code logging.config} 时，加载默认的日志配置文件</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class YxtLog4j2LoggingSystem extends Log4J2LoggingSystem {
    public static final String OS_NAME = System.getProperty("os.name").toLowerCase();
    public static final String LOGS_INFO_FILE_CONFIG = "logs.info.file";
    public static final String LOGS_ERROR_FILE_CONFIG = "logs.error.file";
    public static final String LOGS_AUDIT_FILE_CONFIG = "logs.audit.file";
    private static String LOGS_DIR = "";
    private static String LOGS_ERROR_FILE = "";
    public static String SERVICE_NAME = "";

    private static String LOGS_INFO_FILE = "";

    public YxtLog4j2LoggingSystem(ClassLoader classLoader) {
        super(classLoader);
    }

    private static final String FILE_PROTOCOL = "file";


    private static boolean IS_INIT_FASTER = true;
    private static boolean IS_LODE = true;

    @Override
    public void beforeInitialize() {
        super.beforeInitialize();
        try {
            if (!IS_LODE) {
                return;
            }
            if (IS_INIT_FASTER) {
                printInfo(" log4j2 日志框架预准备 开始加载默认本地配置文件");
            }
            URL url = ResourceUtils.getURL("classpath:log4j2-yxt-default.xml");
            ConfigurationSource configurationSource = getConfigurationSource(url);
            LoggerContext context = (LoggerContext) LogManager.getContext(false);

            XmlConfiguration source = new XmlConfiguration(context, configurationSource);

            // 重新加载配置
            Configurator.reconfigure(source);
            IS_INIT_FASTER = false;
        } catch (Exception e) {
            System.err.println("************************************** log4j2 日志框架预准备初始化失败 **************************************:" + e.getMessage());
        }
    }

    private ConfigurationSource getConfigurationSource(URL url) throws IOException {
        InputStream stream = url.openStream();
        if (FILE_PROTOCOL.equals(url.getProtocol())) {
            return new ConfigurationSource(stream, ResourceUtils.getFile(url));
        }
        return new ConfigurationSource(stream, url);
    }


    /**
     * <p><b>加载默认的日志配置文件</b></p>
     *
     * <p>获取途径：</p>
     * <ol>
     *     <li>从 Apollo 中获取日志配置文件</li>
     *     <li>从 Classpath 目录下获取指定的的日志配置文件</li>
     * </ol>
     *
     * @param initializationContext 日志初始化上下文
     * @param logFile               要加载的文件，可为 {@code null}
     */
    @Override
    protected void loadDefaults(LoggingInitializationContext initializationContext, LogFile logFile) {
        try {
            // 1、从 Apollo 中获取日志配置文件
            if (IS_LODE) {
                printInfo(" log4j2 日志框架 启动 加载apollo配置 开始 ");
                IS_LODE = false;
            }
            SERVICE_NAME = ApolloConfigUtil.getPropertyIgnoreError(CONF_APPLICATION_NAME);
            if (StringUtils.isEmpty(SERVICE_NAME)) {
                for (int i = 0; i < 2; i++) {
                    SERVICE_NAME = ApolloConfigUtil.getPropertyIgnoreError(CONF_APPLICATION_NAME);
                    if (SERVICE_NAME != null) {
                        break;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
            //当本地没有拉取apollo  也没有配置 logging.config 直接返回
            if (StringUtils.isEmpty(SERVICE_NAME)) {
                printInfo(" log4j2 日志框架加载 没有拉取apollo  也没有配置日志路径 用默认配置文件");
                super.loadConfiguration(initializationContext, getLocationByProfiles(initializationContext), logFile);
                return;
            }

            String USER_DIR = ApolloConfigUtil.getPropertyIgnoreError("user.dir");
            if (OS_NAME.contains("win")) {
                USER_DIR = USER_DIR.replace("\\", "/");
            }
            LOGS_DIR = ApolloConfigUtil.getPropertyIgnoreError("logs.dir").replace("${user.dir}", USER_DIR);
            LOGS_INFO_FILE = ApolloConfigUtil.getPropertyIgnoreError(LOGS_INFO_FILE_CONFIG).replace("${logs.dir}", LOGS_DIR).replace("${spring.application.name}", SERVICE_NAME).replace("//", "/");
            LOGS_ERROR_FILE = ApolloConfigUtil.getPropertyIgnoreError(LOGS_ERROR_FILE_CONFIG).replace("${logs.dir}", LOGS_DIR).replace("${spring.application.name}", SERVICE_NAME).replace("//", "/");
            String LOGS_AUDIT_FILE = ApolloConfigUtil.getPropertyIgnoreError(LOGS_AUDIT_FILE_CONFIG).replace("${logs.dir}", LOGS_DIR).replace("${spring.application.name}", SERVICE_NAME).replace("//", "/");
            if (OS_NAME.contains("win")) {
                LOGS_INFO_FILE = LOGS_INFO_FILE.replace("/data/logs", "D:/logs");
                LOGS_ERROR_FILE = LOGS_ERROR_FILE.replace("/data/logs", "D:/logs");
                LOGS_AUDIT_FILE = LOGS_AUDIT_FILE.replace("/data/logs ", "D:/logs");
            }
            ThreadContext.put(LOGS_INFO_FILE_CONFIG, LOGS_INFO_FILE);
            ThreadContext.put(LOGS_ERROR_FILE_CONFIG, LOGS_ERROR_FILE);
            ThreadContext.put(LOGS_AUDIT_FILE_CONFIG, LOGS_AUDIT_FILE);
            ThreadContext.put("logs.dir", LOGS_DIR);
            String pathName = getPathName(SERVICE_NAME);


            URL res = ApplicationStartedEventListener.class.getClassLoader().getResource("");
            String path1 = res.getPath();
            if (ResourceUtils.isJarURL(res)) {//jar启动方式的，将log xml文件放入config目录
                File file1 = new File(pathName);
                if (!file1.exists()) {
                    //循环去拿 是业务linux 启动时  apollo 没有启动完成 没有拉取到 log4j2.xml
                    for (int i = 0; i < 20; i++) {
                        file1 = new File(pathName);
                        if (!file1.exists()) {
                            try {
//                                System.out.println("************************************" + pathName + "***************is mot exists:");
                                Thread.sleep(1000 * i);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
                if (file1.exists()) {
                    printInfo(" log4j2 日志框架启动加载apollo 配置成功");
                    loadConfiguration(initializationContext, pathName, logFile);
                    return;
                }
            } else {//非jar的启动方式，将log xml文件放入classes目录
                URL url = ApplicationStartedEventListener.class.getClassLoader().getResource("");
                File file = new File(url.toURI().getPath() + "log4j2-apollo.properties");

                String xmlPath = prop2Xml(pathName, file);
                if (!StringUtils.isEmpty(xmlPath)) {
                    printInfo(" log4j2 日志框架 非jar的启动 加载apollo 配置成功 ");
                    loadConfiguration(initializationContext, "classpath:log4j2-apollo.properties", logFile);
                    return;
                }
            }

        } catch (Exception e) {
            log.error("get log4j2.properties error", e);
        }
        // 2、加载 Classpath 目录下指定的的日志配置文件
        super.loadConfiguration(initializationContext, getLocationByProfiles(initializationContext), logFile);
    }

    public static void printInfo(String context) {
        System.out.println("\n**************************************" + context + " **************************************\n");
    }

    /**
     * <p><b>根据环境获取日志配置文件</b></p><br/>
     *
     * @param initializationContext 日志初始化上下文
     * @return 日志配置文件
     */
    private String getLocationByProfiles(LoggingInitializationContext initializationContext) {
        String defaultLocation = "classpath:log4j2-yxt-default.xml";
        return defaultLocation;
    }


    private String getPathName(String serviceName) {
        String pathName;
        if (!OS_NAME.contains("linux")) {
            pathName = "C:/opt/data/" + serviceName + "/config-cache/";
        } else {
            //除了win其他系统路径一样
            pathName = "/opt/data/" + serviceName + "/config-cache/";
        }
        String fileName = ApolloConfigUtil.getPropertyIgnoreError("log4j2.fileName");
        if (!StringUtils.isEmpty(fileName)) {
            System.out.println("*********************************YxtLog4j2LoggingSystem apollo get fileName  *****************************fileName:" + fileName);
        }
        {
            fileName = "common.properties";
        }
        String cluster = "default";
        List<String> inputArgs = ManagementFactory.getRuntimeMXBean().getInputArguments();
        for (String in : inputArgs) {
            if (in.contains("Dapollo") && in.contains("cluster")) {
                String[] clusters = in.split("=");
                cluster = clusters[1].replaceAll(" ", "");
            }
        }
        pathName += serviceName + "+" + cluster + "+" + fileName;
        return pathName;
    }

    private String prop2Xml(String path, File file) {
        StringBuffer fileContent = new StringBuffer();
        FileOutputStream out = null;
        InputStreamReader reader = null;
        try {

            File filename = new File(path);
            if (!filename.exists()) {
                return null;
            }
            reader = new InputStreamReader(new FileInputStream(filename));
            BufferedReader br = new BufferedReader(reader);
            int f = 0;
            String line = "";
            line = br.readLine();
            while (line != null) {
                if (!OS_NAME.contains("linux")) {
                    //本地日志格式修改为 localConsole的输出格式
                    if (line.contains("rootLogger.appenderRef.console.ref")) {
                        line = "rootLogger.appenderRef.console.ref = localConsole";
                    } else if (line.contains("${ctx\\:logs.dir}")) {
                        line = line.replace("${ctx\\:logs.dir}", LOGS_DIR).replace("/data/logs", "D:\\logs");
                    } else if (line.contains("${ctx\\:spring.application.name}")) {
                        line = line.replace("${ctx\\:spring.application.name}", SERVICE_NAME);
                    } else if (line.contains("${ctx\\:logs.error.file}")) {
                        line = line.replace("${ctx\\:logs.error.file}", LOGS_ERROR_FILE);
                    } else if (line.contains("${ctx\\:logs.info.file}")) {
                        line = line.replace("${ctx\\:logs.info.file}", LOGS_INFO_FILE);
                    }
                }

                fileContent.append(line).append("\n");
                line = br.readLine();
                f++;
            }

            //java反转义
            String outContent = StringEscapeUtils.unescapeJava(fileContent.toString());

            //生成xml文件
            if (!file.exists()) {
                file.createNewFile();
            } else {
                //先删除再重新创建不然会报错
                file.delete();
                file.createNewFile();
            }
            out = new FileOutputStream(file, true);
            out.write(outContent.getBytes("utf-8"));
        } catch (Exception e) {
            log.error("get log4j2.properties error", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                log.error("out close error", e);
            }
            try {
                if (reader != null) {
                    reader.close();
                }
            }catch (Exception e){
               log.error("reader close error", e);
            }
            return path;
        }
    }

}
