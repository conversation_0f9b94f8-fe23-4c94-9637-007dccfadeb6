package com.yxt.starter.sentinel.spring.web;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.alibaba.csp.sentinel.slots.block.AbstractRule;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

@RunWith(MockitoJUnitRunner.class)
public class SentinelBlockExceptionHandlerTest {

    @Mock
    private BlockException mockBlockException;

    @Mock
    private AbstractRule mockRule;

    @Mock
    private SystemBlockException mockSystemException;

    @InjectMocks
    private YxtCustomBlockExceptionHandler yxtCustomBlockExceptionHandler; // 替换为包含被测方法的类实例

    // 场景1：规则存在且资源非空
    // 预期：直接返回规则资源
    @Test
    public void testGetResourceName_NormalRuleWithResource() throws Exception {
        // Arrange
        String expected = "payment-service";
        when(mockBlockException.getRule()).thenReturn(mockRule);
        when(mockRule.getResource()).thenReturn(expected);

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockBlockException);

        // Assert
        assertThat(result).isEqualTo(expected);
        verify(mockBlockException).getRule();
        verify(mockRule).getResource();
    }

    // 场景2：规则存在但资源为空，非系统级异常
    // 预期：返回空字符串
    @Test
    public void testGetResourceName_EmptyResourceWithNormalException() throws Exception {
        // Arrange
        when(mockBlockException.getRule()).thenReturn(mockRule);
        when(mockRule.getResource()).thenReturn("");

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockBlockException);

        // Assert
        assertThat(result).isEmpty();
        verify(mockBlockException).getRule();
        verify(mockRule).getResource();
    }

    // 场景3：规则存在但资源为空，且为系统级异常
    // 预期：返回带System前缀的资源名
    @Test
    public void testGetResourceName_SystemExceptionWithRule() throws Exception {
        // Arrange
        String systemResource = "cpu-load";
        when(mockSystemException.getRule()).thenReturn(mockRule);
        when(mockRule.getResource()).thenReturn("");
        when(mockSystemException.getResourceName()).thenReturn(systemResource);

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockSystemException);

        // Assert
        assertThat(result).isEqualTo("System_cpu-load");
        verify(mockSystemException).getRule();
        verify(mockSystemException, times(2)).getResourceName();
    }

    // 场景4：规则不存在，但为系统级异常
    // 预期：返回带System前缀的资源名
    @Test
    public void testGetResourceName_SystemExceptionWithoutRule() throws Exception {
        // Arrange
        String systemResource = "mem-usage";
        when(mockSystemException.getRule()).thenReturn(null);
        when(mockSystemException.getResourceName()).thenReturn(systemResource);

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockSystemException);

        // Assert
        assertThat(result).isEqualTo("System_mem-usage");
        verify(mockSystemException).getRule();
        verify(mockSystemException, times(2)).getResourceName();
    }

    // 场景5：规则不存在且非系统级异常
    // 预期：返回空字符串
    @Test
    public void testGetResourceName_NoRuleWithNormalException() throws Exception {
        // Arrange
        when(mockBlockException.getRule()).thenReturn(null);

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockBlockException);

        // Assert
        assertThat(result).isEmpty();
        verify(mockBlockException).getRule();
    }

    // 场景6：系统级异常但资源名为空
    // 预期：返回System_前缀+空字符串
    @Test
    public void testGetResourceName_SystemExceptionWithEmptyResource() throws Exception {
        // Arrange
        when(mockSystemException.getRule()).thenReturn(null);
        when(mockSystemException.getResourceName()).thenReturn("");

        // Act
        String result = Whitebox.invokeMethod(yxtCustomBlockExceptionHandler, "getResourceName", mockSystemException);

        // Assert
        assertThat(result).isEqualTo("System_");
        verify(mockSystemException).getRule();
        verify(mockSystemException, times(2)).getResourceName();
    }

}