package com.yxt.starter.configuration;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

/**
 *
 * Created by peng on 19/6/19.
 */
@EnableApolloConfig
@ConditionalOnProperty(prefix = "apollo.bootstrap", name = "enabled", havingValue = "true")
class ApolloConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ApolloConfigChanged buildApolloConfigChanged() {
        return new ApolloConfigChanged();
    }
}