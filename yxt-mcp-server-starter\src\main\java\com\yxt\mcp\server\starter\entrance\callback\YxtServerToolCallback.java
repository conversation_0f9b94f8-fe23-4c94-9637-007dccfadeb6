package com.yxt.mcp.server.starter.entrance.callback;

import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.support.constants.McpServerConstants;
import com.yxt.mcp.server.starter.support.constants.YxtServerAuthHeadFiledConstants;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 * @Description 基于微服务的Callback
 * @Date 2025/06/19/10:28
 */
public class YxtServerToolCallback extends AbstractToolCallback {

    private static final Logger logger = LoggerFactory.getLogger(YxtServerToolCallback.class);

    private RestTemplate restTemplate;

    public YxtServerToolCallback(YxtToolDefinition toolDefinition, ApplicationContext applicationContext) {
        super(toolDefinition, applicationContext);
        this.restTemplate = applicationContext.getBean(RestTemplate.class);
    }


    @Override
    public String process(YxtAuthResult yxtAuthResult, String toolInput) {
        String serverName = getYxtToolDefinition().getProtocolMeta()
            .getString(McpServerConstants.FIELD_PROTOCOL_META_SERVER_NAME);
        String serverUrl = getYxtToolDefinition().getProtocolMeta()
            .getString(McpServerConstants.FIELD_PROTOCOL_META_SERVER_URL);

        try {
            String url = UriComponentsBuilder.newInstance()
                .scheme("http")
                .host(serverName)
                .path(serverUrl)
                .build()
                .toUriString();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            setAuthInfo(headers, yxtAuthResult);
            HttpEntity<String> entity = new HttpEntity<>(toolInput, headers);
            String result = restTemplate.postForObject(url, entity, String.class);
            return result;
        } catch (RestClientException e) {
            logger.error("调用工具失败 call toolInput={}, serverName={}, serverUrl={}", toolInput, serverName,
                serverUrl, e);
            throw e;
        }
    }

    private void setAuthInfo(HttpHeaders headers, YxtAuthResult yxtAuthResult) {
        headers.set(YxtServerAuthHeadFiledConstants.HEAD_EMP_CODE, yxtAuthResult.yxtEmpCode());
        headers.set(YxtServerAuthHeadFiledConstants.HEAD_USER_ACCOUNT, yxtAuthResult.operatorLogin());
        headers.set(YxtServerAuthHeadFiledConstants.HEAD_USER_ID, yxtAuthResult.yxtUserId());
        headers.set(YxtServerAuthHeadFiledConstants.HEAD_USER_ZH_NAME, yxtAuthResult.operatorName());
        headers.set(YxtServerAuthHeadFiledConstants.HEAD_OPERATOR_SOURCE, yxtAuthResult.operatorSource());
    }
}
