package com.yxt.mcp.server.starter.entrance.callback;

import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.support.channel.sdk.JiraRestClient;
import com.yxt.mcp.server.starter.support.constants.McpServerConstants;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description jira渠道
 * @Date 2025/06/24/9:26
 */
public class JiraToolCallback extends AbstractToolCallback {

    public JiraToolCallback(YxtToolDefinition toolDefinition, ApplicationContext applicationContext) {
        super(toolDefinition, applicationContext);
    }

    @Override
    public String process(YxtAuthResult yxtAuthResult, String toolInput) {
        // 获取原始 URL 模板
        String urlTemplate = getYxtToolDefinition().getProtocolMeta()
            .getString(McpServerConstants.FIELD_PROTOCOL_META_URL);
        String url = resolveUrlTemplate(urlTemplate, toolInput);
        return getBean(JiraRestClient.class).get(url);
    }
}
