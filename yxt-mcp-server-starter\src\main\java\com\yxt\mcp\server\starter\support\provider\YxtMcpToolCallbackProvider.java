package com.yxt.mcp.server.starter.support.provider;

import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;

/**
 * <AUTHOR>
 * @Description yxt工具处理器提供者
 * @Date 2025/06/12/16:38
 */
public class YxtMcpToolCallbackProvider implements ToolCallbackProvider {

    private final ToolCallback[] toolCallbacks;

    public YxtMcpToolCallbackProvider(ToolCallback[] toolCallbacks) {
        this.toolCallbacks = toolCallbacks;
    }

    @Override
    public ToolCallback[] getToolCallbacks() {
        return this.toolCallbacks;
    }


}
