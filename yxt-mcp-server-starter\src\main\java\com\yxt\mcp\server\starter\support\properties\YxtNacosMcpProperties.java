package com.yxt.mcp.server.starter.support.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @Description Nacos配置
 * @Date 2025/06/12/16:57
 */
@ConfigurationProperties(prefix = YxtNacosMcpProperties.CONFIG_PREFIX)
public class YxtNacosMcpProperties {

    public static final String CONFIG_PREFIX = "spring.ai.yxt.mcp.nacos";

    /**
     * nacos地址
     */
    private String serverAddr;
    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 超时时间
     */
    private Integer timeout;

    /**
     * 工具持久化数据id
     */
    private String toolDataId;

    /**
     * 授权持久化数据id
     */
    private String authDataId;

    /**
     * 应用持久化数据id
     */
    private String accessDataId;

    private String group;

    public String getServerAddr() {
        return serverAddr;
    }

    public void setServerAddr(String serverAddr) {
        this.serverAddr = serverAddr;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public String getAuthDataId() {
        return authDataId;
    }

    public void setAuthDataId(String authDataId) {
        this.authDataId = authDataId;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getToolDataId() {
        return toolDataId;
    }

    public void setToolDataId(String toolDataId) {
        this.toolDataId = toolDataId;
    }

    public String getAccessDataId() {
        return accessDataId;
    }

    public void setAccessDataId(String accessDataId) {
        this.accessDataId = accessDataId;
    }
}
