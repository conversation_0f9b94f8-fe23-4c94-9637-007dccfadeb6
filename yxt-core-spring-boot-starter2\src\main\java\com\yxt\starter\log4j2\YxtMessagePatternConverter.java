
package com.yxt.starter.log4j2;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.core.pattern.PatternConverter;
import org.apache.logging.log4j.core.pattern.TextRenderer;
import org.apache.logging.log4j.message.Message;
import org.apache.logging.log4j.message.MultiformatMessage;
import org.apache.logging.log4j.util.MultiFormatStringBuilderFormattable;
import org.apache.logging.log4j.util.PerformanceSensitive;
import org.apache.logging.log4j.util.StringBuilderFormattable;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

/**
 * Returns the event's rendered message in a StringBuilder.
 */
@Plugin(name = "YxtMessagePatternConverter", category = PatternConverter.CATEGORY)
@ConverterKeys({"yxtm", "yxtmsg", "yxtmessage"})
@PerformanceSensitive("allocation")
@Slf4j
public class YxtMessagePatternConverter extends LogEventPatternConverter {

    private static final String LOOKUPS = "lookups";
    private static final String NOLOOKUPS = "nolookups";

    private YxtMessagePatternConverter() {
        super("Yxtm", "yxtm");
    }



    /**
     * Obtains an instance of pattern converter.
     *
     * @param config
     *            The Configuration.
     * @param options
     *            options, may be null.
     * @return instance of pattern converter.
     */
    public static YxtMessagePatternConverter newInstance(final Configuration config, final String[] options) {
        final String[] formats = withoutLookupOptions(options);
//        final TextRenderer textRenderer = loadMessageRenderer(formats);
        YxtMessagePatternConverter result = formats == null || formats.length == 0
                ? SimpleMessagePatternConverter.INSTANCE
                : new FormattedMessagePatternConverter(formats);

        return result;
    }

    private static String[] withoutLookupOptions(final String[] options) {
        if (options == null || options.length == 0) {
            return options;
        }
        final List<String> results = new ArrayList<>(options.length);
        for (String option : options) {
            if (LOOKUPS.equalsIgnoreCase(option) || NOLOOKUPS.equalsIgnoreCase(option)) {
                LOGGER.info("The {} option will be ignored. Message Lookups are no longer supported.", option);
            } else {
                results.add(option);
            }
        }
        return results.toArray(Strings.EMPTY_ARRAY);
    }

    @Override
    public void format(final LogEvent event, final StringBuilder toAppendTo) {
        throw new UnsupportedOperationException();
    }

    private static final class SimpleMessagePatternConverter extends YxtMessagePatternConverter {
        private static final YxtMessagePatternConverter INSTANCE = new SimpleMessagePatternConverter();

        /**
         * {@inheritDoc}
         */
        @Override
        public void format(final LogEvent event, StringBuilder toAppendTo) {
            try {
                final Message msg = event.getMessage();
                if (msg instanceof StringBuilderFormattable) {
                    ((StringBuilderFormattable) msg).formatTo(toAppendTo);
                } else if (msg != null) {
                    toAppendTo.append(msg.getFormattedMessage());
                }
                String message = toAppendTo.toString();
                //JSONObject.toJSONString 转义 方便elk 收集
                if (message.contains("{\"") && message.contains("}")){
                    String fast = message.substring(0, message.indexOf("{\""));
                    String substring = message.substring(message.indexOf("{\""));
                    String s1 = StringEscapeUtils.escapeJson(substring).replaceAll("\\\\n", "");
                    toAppendTo.replace(0, toAppendTo.length(), fast + s1);
                }
            }catch (Exception e){
                log.info("YxtMessagePatternConverter error:", e);
            }

        }
    }

    private static final class FormattedMessagePatternConverter extends YxtMessagePatternConverter {

        private final String[] formats;

        FormattedMessagePatternConverter(final String[] formats) {
            this.formats = formats;
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public void format(final LogEvent event, final StringBuilder toAppendTo) {
            try {
                final Message msg = event.getMessage();
                if (msg instanceof StringBuilderFormattable) {
                    if (msg instanceof MultiFormatStringBuilderFormattable) {
                        ((MultiFormatStringBuilderFormattable) msg).formatTo(formats, toAppendTo);
                    } else {
                        ((StringBuilderFormattable) msg).formatTo(toAppendTo);
                    }
                } else if (msg != null) {
                    toAppendTo.append(
                            msg instanceof MultiformatMessage
                                    ? ((MultiformatMessage) msg).getFormattedMessage(formats)
                                    : msg.getFormattedMessage());
                }
            }catch (Exception e){
                log.info("YxtMessagePatternConverter error:", e);
            }
        }
    }

    private static final class RenderingPatternConverter extends YxtMessagePatternConverter {

        private final YxtMessagePatternConverter delegate;
        private final TextRenderer textRenderer;

        RenderingPatternConverter(final YxtMessagePatternConverter delegate, final TextRenderer textRenderer) {
            this.delegate = delegate;
            this.textRenderer = textRenderer;
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public void format(final LogEvent event, final StringBuilder toAppendTo) {
            final StringBuilder workingBuilder = new StringBuilder(80);
            delegate.format(event, workingBuilder);
            textRenderer.render(workingBuilder, toAppendTo);
        }
    }
}
