package com.yxt.starter.sentinel.spring.web;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

import com.alibaba.csp.sentinel.adapter.spring.webmvc_v6x.config.SentinelWebMvcConfig;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRule;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.yxt.starter.sentinel.constants.YxtSentinelConstants;
import java.util.Arrays;
import java.util.Optional;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.http.HttpMethod;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.servlet.HandlerMapping;

@RunWith(MockitoJUnitRunner.class)
public class YxtCustomRequestOriginParserTest {

    @Mock
    private SentinelWebMvcConfig mockConfig;

    @InjectMocks
    private YxtCustomRequestOriginParser yxtCustomRequestOriginParserUnderTest;

    /**
     * 没有head头场景
     */
    @Test
    public void testParseOrigin_NotHead_ReturnsNull() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();

        // Run the test
        final String result = yxtCustomRequestOriginParserUnderTest.parseOrigin(request);

        // Verify the results
        assertTrue(result == null);
    }

    @Test
    public void testGetOrigin() {
        String resourceName = "sentinel";
        String origin = "assist-synthesis";
        final MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader(YxtSentinelConstants.HEAD_SERVICE_NAME_KEY, origin);
        request.setAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE, resourceName);
        request.setMethod(HttpMethod.POST.name());
        // Setup
        when(mockConfig.getUrlCleaner()).thenReturn(null);
        when(mockConfig.isHttpMethodSpecify()).thenReturn(false);

        //--------------------------无授权规则场景----------------------------
        Optional<String> result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);

        assertTrue(!result.isPresent());

        //--------------------------有授权规则但未配置用户限流场景----------------------------
        AuthorityRule authorityRule2 = new AuthorityRule();
        authorityRule2.setResource("AuthorityRule");
        AuthorityRuleManager.loadRules(Arrays.asList(authorityRule2));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());

        //--------------------------有流控规则但未配置来源限流场景----------------------------
        FlowRule flowRule2 = new FlowRule();
        flowRule2.setResource("FlowRule");
        FlowRuleManager.loadRules(Arrays.asList(flowRule2));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());

        //--------------------------限流规则名字匹配但未配置来源限流场景----------------------------
        FlowRule flowRule3 = new FlowRule();
        flowRule3.setResource(resourceName);
        FlowRuleManager.loadRules(Arrays.asList(flowRule3));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());
        //--------------------------授权规则名字匹配但未配置来源限流场景----------------------------
        AuthorityRule authorityRule3 = new AuthorityRule();
        authorityRule3.setResource("resourceName");
        AuthorityRuleManager.loadRules(Arrays.asList(authorityRule3));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());

        //--------------------------限流规则名字匹配来源不匹配场景---------------------------
        FlowRule flowRule4 = new FlowRule();
        flowRule4.setResource(resourceName);
        flowRule4.setLimitApp("127.0.0.9");
        FlowRuleManager.loadRules(Arrays.asList(flowRule4));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());
        //--------------------------授权规则名字匹配但来源不匹配侧场景----------------------------
        AuthorityRule authorityRule4 = new AuthorityRule();
        authorityRule4.setResource("resourceName");
        authorityRule4.setLimitApp("127.0.0.9");
        AuthorityRuleManager.loadRules(Arrays.asList(authorityRule4));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(!result.isPresent());

        //--------------------------限流规则名字匹配有配置来源限流场景----------------------------

        FlowRule flowRule6 = new FlowRule();
        flowRule6.setResource(resourceName);
        flowRule6.setLimitApp(origin);
        FlowRuleManager.loadRules(Arrays.asList(flowRule6));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(result.isPresent());

        //--------------------------授权规则名字匹配有配置来源限流场景----------------------------

        AuthorityRule authorityRule6 = new AuthorityRule();
        authorityRule6.setResource(resourceName);
        authorityRule6.setLimitApp(origin);
        AuthorityRuleManager.loadRules(Arrays.asList(authorityRule6));
        result = yxtCustomRequestOriginParserUnderTest.getOrigin(request);
        assertTrue(result.isPresent());


    }

    @Test
    public void testGetResourceName() {
        String resourceName = "sentinel";
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        request.setAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE, resourceName);
        request.setMethod(HttpMethod.POST.name());
        when(mockConfig.getUrlCleaner()).thenReturn(null);
        when(mockConfig.isHttpMethodSpecify()).thenReturn(false);

        //-------------------------------不用匹配请求方法场景-----------------------------------
        // Run the test
        String result = yxtCustomRequestOriginParserUnderTest.getResourceName(request);

        // Verify the results
        assertEquals(resourceName, result);

        //-------------------------------需要拼接方法场景-----------------------------------
        SentinelWebMvcConfig sentinelWebMvcConfig = new SentinelWebMvcConfig();
        sentinelWebMvcConfig.setHttpMethodSpecify(true);
        Whitebox.setInternalState(yxtCustomRequestOriginParserUnderTest, "config",
            sentinelWebMvcConfig);
        result = yxtCustomRequestOriginParserUnderTest.getResourceName(request);
        assertEquals(String.format("%s:%s", request.getMethod(), resourceName), result);
    }
}


