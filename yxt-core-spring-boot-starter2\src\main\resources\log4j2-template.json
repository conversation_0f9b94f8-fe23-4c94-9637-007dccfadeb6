{"@timestamp": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd HH:mm:ss.SSS", "timeZone": "Asia/Shanghai"}}, "level": {"$resolver": "level", "field": "name"}, "traceId": {"$resolver": "pattern", "pattern": "%replace{%traceId}{TID: }{}", "stackTraceEnabled": false}, "app": {"$resolver": "mdc", "key": "spring.application.name"}, "thread": {"$resolver": "thread", "field": "name"}, "method": {"$resolver": "pattern", "pattern": "%c.%M:%L", "stackTraceEnabled": false}, "msg": {"$resolver": "message", "stringified": true}, "stackTrace": {"$resolver": "exception", "field": "stackTrace", "stackTrace": {"stringified": true}}}