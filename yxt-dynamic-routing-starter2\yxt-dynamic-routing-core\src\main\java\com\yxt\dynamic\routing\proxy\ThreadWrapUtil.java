package com.yxt.dynamic.routing.proxy;

import java.sql.SQLException;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/24 14:29
 */
public class ThreadWrapUtil {

    public static Object wrap(Object obj) throws SQLException {
        if(obj==null){
            return obj;
        }
        if(obj instanceof MDCRunnable || obj instanceof MDCCallable || obj instanceof MDCFutureTask){
            return obj;
        }else if(obj instanceof Runnable) {
            MDCRunnable callable = new MDCRunnable((Runnable) obj);
            obj = callable;
        }else if(obj instanceof Callable){
            MDCCallable callable = new MDCCallable((Callable<Object>) obj);
            obj = callable;
        }else if(obj instanceof FutureTask) {
            MDCFutureTask callable = new MDCFutureTask((FutureTask) obj);
            obj = callable;
        }
        return obj;
    }
}
