package com.yxt.starter.filter;

import java.io.IOException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Created by peng on 19/12/19.
 */
@Order(1)
@Data
@WebFilter(urlPatterns = "/*", asyncSupported = true)
@Slf4j
public class TraceIdFilter implements Filter {

    public static final String TRACE_ID_HEADER_NAME = "traceId";

    private static final String TRACE_START_TIMESTAMP_ATTR_NAME = "MY_TRACE_START_TIMESTAMP";

    //如果该值为true，traceId为空，则重新设置traceId，如果traceId不为空，该配置无效
    @Value("${trace.reset.enable:false}")
    private String resetEnable;
     @Value("${spring.application.name}")
    private String serverName;

     public static final String SERVER_NAME = "spring.application.name";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            //如果开启重新设置trace
            if (StringUtils.equals(resetEnable, "true") && StringUtils.isBlank(MDC.get(TRACE_ID_HEADER_NAME))) {
                //如果mdc中不存在
                MDC.put(TRACE_ID_HEADER_NAME, generateTraceId());
            }
            HttpServletResponse httpServletResponse = (HttpServletResponse) response;
            String traceId = httpServletResponse.getHeader(TRACE_ID_HEADER_NAME);
            if (StringUtils.isEmpty(traceId)) {
                traceId = TraceContext.traceId();
                if (StringUtils.equals(resetEnable, "true") && StringUtils.isBlank(traceId) && StringUtils.isNotBlank(MDC.get(TRACE_ID_HEADER_NAME))) {
                    //如果mdc中存在切header中不存在
                    httpServletResponse.setHeader(TRACE_ID_HEADER_NAME, MDC.get(TRACE_ID_HEADER_NAME));
                } else {
                    httpServletResponse.setHeader(TRACE_ID_HEADER_NAME, traceId);
                }
            }
            MDC.put(TraceIdFilter.SERVER_NAME, serverName);

            request.setAttribute(TRACE_START_TIMESTAMP_ATTR_NAME, System.currentTimeMillis());
            chain.doFilter(request, response);
        } finally {
            if (StringUtils.isNotBlank(MDC.get(TRACE_ID_HEADER_NAME))) {
                MDC.remove(TRACE_ID_HEADER_NAME);
            }
        }
    }

    public static long getStartTimestamp(HttpServletRequest request) {
        Long start = (Long) request.getAttribute(TRACE_START_TIMESTAMP_ATTR_NAME);
        return start == null ? -1 : start;
    }


    /**
     * 生成traceId
     *
     * @return
     */
    private String generateTraceId() {
        // 实际应用中，可以使用更复杂的逻辑生成Trace ID
        return java.util.UUID.randomUUID().toString();
    }

}
