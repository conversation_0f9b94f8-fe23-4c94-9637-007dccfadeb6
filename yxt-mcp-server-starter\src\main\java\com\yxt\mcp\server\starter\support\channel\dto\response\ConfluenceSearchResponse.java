package com.yxt.mcp.server.starter.support.channel.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfluenceSearchResponse {

    private List<SearchResult> results;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SearchResult {

        private Content content;
        private String excerpt;
        private String pageId;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Content {

        private String id;
        private String type;
        private String status;
        private String title;
        private Links _links;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Links {

        private String webui;
    }
}