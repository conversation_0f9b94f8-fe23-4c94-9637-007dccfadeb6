package com.yxt.starter.exception.whitelist;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 告警白名单
 *
 * <AUTHOR>
 * @date 2024/01/22 13:36
 **/
@Data
public class ErrorCodeAlarmWhiteCode {

    /**
     * 一级code
     */
    private List<String> codeList = new ArrayList<>();

    /**
     * 二级code
     */
    private List<String> subCodeList = new ArrayList<>();

    /**
     * msg模糊匹配
     * 支持*A*B*，支持*在任意位置，只需写一个*即可
     */
    private List<String> msgBlurList = new ArrayList<>();
}
