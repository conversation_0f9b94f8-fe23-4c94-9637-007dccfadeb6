package com.yxt.starter.filter.assist;

import com.yxt.lang.constants.OperatorSourceEnum;
import com.yxt.lang.dto.OperateContext;
import com.yxt.starter.filter.assist.config.OperateContextFilterConfig;
import com.yxt.starter.filter.assist.constants.OperateContextFilterConstants;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


/**
 * 登录态过滤器（目前仅涉及B端处理逻辑）
 *
 * <AUTHOR>
 * @date 2023年10月12日 14:59
 */
@Slf4j
@ConditionalOnMissingBean(OperateContextFilterConfig.class)
public class OperateContextFilter implements Filter {

    /**
     * 初始化
     *
     * @param filterConfig The configuration information associated with the filter instance being initialised
     * @throws ServletException if the initialisation fails
     */
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.debug("RequiredAuthFilter init...");
    }

    /**
     * 过滤
     *
     * @param servletRequest  The request to process
     * @param servletResponse The response associated with the request
     * @param filterChain     Provides access to the next filter in the chain for this filter to pass the request and
     *                        response to for further processing
     * @throws IOException      if an I/O error occurs during this filter's processing of the request
     * @throws ServletException if the processing fails for any other reason
     */
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException {
        ParameterRequestWrapper parameterRequestWrapper = new ParameterRequestWrapper(
            (HttpServletRequest) servletRequest);
        // 从 header 获取
        String userAccount = parameterRequestWrapper.getHeader(OperateContextFilterConstants.USER_NAME);
        String empCode = parameterRequestWrapper.getHeader(OperateContextFilterConstants.EMP_CODE);
        String userId = parameterRequestWrapper.getHeader(OperateContextFilterConstants.USER_ID);
        String zhName = parameterRequestWrapper.getHeader(OperateContextFilterConstants.USER_ZH_NAME);
        if (StringUtils.isNotBlank(zhName)) {
            zhName = URLDecoder.decode(zhName, "UTF-8");
        }

        // 构建 operateContext
        OperateContext operateContext = new OperateContext();
        operateContext.setOperatorId(empCode);
        operateContext.setOperatorUserId(userId);
        operateContext.setOperatorLogin(userAccount);
        operateContext.setOperatorName(zhName);
        operateContext.setOperatorSource(OperatorSourceEnum.SSO);

        Map<String, String[]> originParameterMap = parameterRequestWrapper.getParameterMap();
        Map<String, Object> parameterMap;
        if (MapUtils.isEmpty(originParameterMap)) {
            parameterMap = new HashMap<>();
        } else {
            parameterMap = new HashMap<>(originParameterMap);
        }

        this.addOperateContext(parameterRequestWrapper, parameterMap, operateContext);
        filterChain.doFilter(parameterRequestWrapper, servletResponse);
    }

    /**
     * 销毁
     */
    @Override
    public void destroy() {
        log.debug("RequiredAuthFilter destroy...");
    }

    /**
     * 将 operateContext 的属性放入 parameter
     *
     * @param parameterRequestWrapper request
     * @param parameterMap            parameterMap
     * @param operateContext          登录态上下文
     */
    private void addOperateContext(ParameterRequestWrapper parameterRequestWrapper, Map<String, Object> parameterMap,
        OperateContext operateContext) {
        parameterMap.put("operatorId", operateContext.getOperatorId());
        parameterMap.put("operatorUserId", operateContext.getOperatorUserId());
        parameterMap.put("operatorName", operateContext.getOperatorName());
        parameterMap.put("operatorLogin", operateContext.getOperatorLogin());
        parameterMap.put("operatorSource", operateContext.getOperatorSource());
        parameterRequestWrapper.addAllParameters(parameterMap);
    }
}
