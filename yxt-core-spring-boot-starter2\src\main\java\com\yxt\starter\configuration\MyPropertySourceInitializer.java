package com.yxt.starter.configuration;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.PropertySource;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;

@Configuration
@Slf4j
@Order(0)
public class MyPropertySourceInitializer implements ApplicationContextInitializer {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        applicationContext.getEnvironment().getPropertySources().addFirst(new MyEncryptPropertySource());
    }

    public static class MyEncryptPropertySource extends PropertySource<MyEncrypt> {

        private static final String ENCYPT_PROPERTY_SOURCE_NAME = "myEncrypt";

        private static final String PREFIX = "myEncrypt.";

        private static final String TYPE_DES = "des";


        MyEncryptPropertySource() {
            super(ENCYPT_PROPERTY_SOURCE_NAME, new MyEncrypt());
        }

        @Override
        public Object getProperty(String name) {
            if (!name.startsWith(PREFIX)) {
                return null;
            }
            if (logger.isDebugEnabled()) {
                logger.debug("Generating encrypt property for '" + name + "'");
            }
            return decrypt(name.substring(PREFIX.length()));
        }

        private String decrypt(String expr) {
            int idx = expr.indexOf("(");
            String type = expr.substring(0, idx);
            String value = expr.substring(idx + 1, expr.length() - 1);
            if (TYPE_DES.equals(type)) {
                try {
                    return MyEncrypt.decrypt(value, null);
                } catch (Exception e) {
                    throw new IllegalArgumentException("decrypt error! value is: " + value, e);
                }
            }
            return null;

        }
    }

    private static class MyEncrypt {

        static final String DEFAULT_KEY = "hydeesoft20201117";

        private static Key convertSecretKey;

        static {
            try {
                convertSecretKey = generateSecret(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private static Key generateSecret(String key) throws Exception {
            if (key == null) {
                key = System.getProperty("hydee.config.encrypt.key", DEFAULT_KEY);
            }
            byte[] bytesKey = key.getBytes();
            DESKeySpec desKeySpec = new DESKeySpec(bytesKey);
            SecretKeyFactory factory = SecretKeyFactory.getInstance("DES");
            return factory.generateSecret(desKeySpec);
        }

        private static String encrypt(String data, String key) throws Exception {
            Key secretKey = convertSecretKey;
            if (StringUtils.isNotBlank(key)) {
                secretKey = generateSecret(key);
            }
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] enData = Base64.getEncoder().encode(data.getBytes(StandardCharsets.UTF_8));
            byte[] result = cipher.doFinal(enData);
            return Hex.encodeHexString(result);
        }

        private static String decrypt(String data, String key) throws Exception {
            Key secretKey = convertSecretKey;
            if (StringUtils.isNotBlank(key)) {
                secretKey = generateSecret(key);
            }
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] hdata = Hex.decodeHex(data.toCharArray());
            byte[] result = cipher.doFinal(hdata);
            byte[] decode = Base64.getDecoder().decode(result);
            return new String(decode, StandardCharsets.UTF_8);
        }
    }
}
