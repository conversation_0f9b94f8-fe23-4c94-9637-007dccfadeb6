package com.yxt.dynamic.routing.agent.core;

import javassist.CtClass;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/21 17:57
 */
public class JClass {

    private List<JAnnotation> jAnnotationList = new ArrayList<>();

    private List<JMethod> jMethods = new ArrayList<>();
    private List<JField> jFields = new ArrayList<>();
    private List<JConstructor> jConstructor = new ArrayList<>();

    private JTransformer jTransformer;

    private String className;
    private String loadClassName;

    private boolean needWriteClass;

    private boolean root;

    boolean isNeedWriteClass() {
        return needWriteClass;
    }

    /**
     * 标记该类为
     *
     * @return
     */
    public JClass root() {
        this.root = true;
        return this;
    }


    protected String getSimpleName() {
        String[] split = this.className.split("\\.");
        return split[split.length - 1];
    }

    public JClass(String className, String loadClassName, JTransformer jTransformer) {
        this.className = className;
        this.loadClassName = loadClassName;
        this.jTransformer = jTransformer;
    }

    public JTransformer then() {
        return jTransformer;
    }

    public JAnnotation jAnnotation(String annName) {
        JAnnotation jAnnotation = new JAnnotation(this, annName);
        jAnnotationList.add(jAnnotation);
        return jAnnotation;
    }

    public JMethod jMethod(String methodName) {
        JMethod jMethod = new JMethod(methodName, this);
        jMethods.add(jMethod);
        return jMethod;
    }

    public JMethod jMethod(String methodName,String ... paramClass) {
        JMethod jMethod = new JMethod(methodName, this,paramClass);
        jMethods.add(jMethod);
        return jMethod;
    }

    protected CtClass apply(CtClass ctClass) {
        ctClass = removeAnnotationApply(ctClass);
        fieldApply(ctClass);
        constructorApply(ctClass);
        for (JMethod jMethod : jMethods) {
            if (jMethod.isRemoved()) {
                this.needWriteClass = true;
            }
            ctClass = jMethod.apply(ctClass);
        }
        return ctClass;
    }

    private void constructorApply(CtClass ctClass) {
        for (JConstructor constructor : jConstructor) {
            try {
                ctClass = constructor.apply(ctClass);
            } catch (Exception e) {
                System.out.println("javasisit constructor error:" +this.getClassName()+" " + e.getMessage());
            }
        }
    }

    private void fieldApply(CtClass ctClass) {
        for (JField jField : jFields) {
            try {
                jField.apply(ctClass);
            } catch (Exception e) {
                System.out.println("javasisit field error:" +this.getClassName()+" " + e.getMessage());
            }
        }
    }

    private CtClass removeAnnotationApply(CtClass ctClass) {
        // 获取当前的AnnotationsAttribute
        AnnotationsAttribute annotationsAttr = (AnnotationsAttribute) ctClass.getClassFile().getAttribute(AnnotationsAttribute.visibleTag);

        if (annotationsAttr != null) {
            // 创建一个新的ConstPool（如果需要的话）
            ConstPool constPool = ctClass.getClassFile().getConstPool();
            // 过滤掉特定注解
            AnnotationsAttribute newAnnotationsAttr = new AnnotationsAttribute(constPool, AnnotationsAttribute.visibleTag);
            newAnnotationsAttr.setAnnotations(annotationsAttr.getAnnotations());
            for (JAnnotation jAnnotation : this.jAnnotationList) {
                jAnnotation.removeClassApply(newAnnotationsAttr);
            }
            // 替换原有的AnnotationsAttribute
            ctClass.getClassFile().addAttribute(newAnnotationsAttr);
        }
        return ctClass;
    }


    protected String getClassName() {
        return this.className;
    }

    String getLoadClassName() {
        return loadClassName;
    }

    boolean isRoot() {
        return root;
    }

    String getPackageName() {
        int i = className.lastIndexOf(".");
        String substring = className.substring(0, i);
        return substring;
    }

    String getRelativePath(){
        String packageName = getPackageName();
        return packageName.replaceAll("\\.","/")+"/";
    }

    public JField jField(String fieldName) {
        JField jField = new JField(this,fieldName);
        this.jFields.add(jField);
        return jField;
    }

    public JConstructor jConstructor(String ... paramsClass) {
        JConstructor jConstructor = new JConstructor(this,paramsClass);
        this.jConstructor.add(jConstructor);
        return jConstructor;
    }
}
