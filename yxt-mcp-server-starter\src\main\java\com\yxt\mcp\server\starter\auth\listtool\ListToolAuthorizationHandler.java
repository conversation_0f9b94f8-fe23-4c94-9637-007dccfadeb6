package com.yxt.mcp.server.starter.auth.listtool;

import com.yxt.mcp.server.starter.support.constants.AuthTargetTypeEnum;
import com.yxt.mcp.server.starter.support.datasource.dto.AuthorizationRule;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/06/16/16:22
 */
public interface ListToolAuthorizationHandler {

    /**
     * 判断当前处理器是否适用于指定的目标类型。
     *
     * @param targetType
     * @return
     */
    boolean support(AuthTargetTypeEnum targetType);

    /**
     * 根据用户 ID、应用 ID、业务场景和授权规则，返回当前用户有权限访问的工具名称列表。
     *
     * @param userId 用户 ID
     * @param appId 应用 ID
     * @param businessScenario 业务场景
     * @param authorizationRules 授权规则
     * @return 当前用户有权限访问的工具名称列表
     */
    List<String> handle(String userId, String appId, String businessScenario,
        List<AuthorizationRule> authorizationRules);
}
