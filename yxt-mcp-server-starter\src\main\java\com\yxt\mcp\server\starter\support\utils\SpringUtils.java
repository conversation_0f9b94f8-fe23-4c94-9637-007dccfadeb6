package com.yxt.mcp.server.starter.support.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    private static final Logger logger = LoggerFactory.getLogger(SpringUtils.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 对应的被管理类有别名时使用
     *
     * @param name
     * @param <T>
     * @return
     * @throws BeansException
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) throws BeansException {
        if (applicationContext == null) {
            logger.warn("applicationContext  is null");
            return null;
        }
        return (T) applicationContext.getBean(name);
    }

    /**
     * 在对应的注解内未使用别名时 使用
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext == null) {
            logger.warn("applicationContext  is null");
            return null;
        }
        return applicationContext.getBean(clazz);
    }

}
