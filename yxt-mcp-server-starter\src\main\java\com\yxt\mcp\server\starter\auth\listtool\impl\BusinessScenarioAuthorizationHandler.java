package com.yxt.mcp.server.starter.auth.listtool.impl;

import com.google.common.collect.Lists;
import com.yxt.mcp.server.starter.auth.listtool.ListToolAuthorizationHandler;
import com.yxt.mcp.server.starter.support.constants.AuthTargetTypeEnum;
import com.yxt.mcp.server.starter.support.datasource.dto.AuthorizationRule;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 基于业务场景鉴权
 * @Date 2025/06/16/16:33
 */
@Service
public class BusinessScenarioAuthorizationHandler implements ListToolAuthorizationHandler {

    @Override
    public boolean support(AuthTargetTypeEnum targetType) {
        return AuthTargetTypeEnum.BUSINESSS_CENARIO.equals(targetType);
    }

    /**
     * 根据 businessScenario 和授权规则，获取当前业务场景下可访问的工具名称列表
     * userId: 用户 ID（未使用）。
     * appId: 应用 ID（未使用）。
     * businessScenario: 业务场景标识符，用于匹配授权规则。
     * authorizationRules: 所有授权规则列表。
     */
    @Override
    public List<String> handle(String userId, String appId, String businessScenario,
        List<AuthorizationRule> authorizationRules) {
        // 筛选出所有目标类型为 BUSINESSS_CENARIO 且目标列表中包含当前 businessScenario 的授权规则
        Set<AuthorizationRule> appAuthRuleList = authorizationRules.stream()
            .filter(c -> AuthTargetTypeEnum.BUSINESSS_CENARIO.equals(c.getTargetType()) && c.getTargetList()
                .contains(businessScenario))
            .collect(
                Collectors.toSet());
        if (CollectionUtils.isEmpty(appAuthRuleList)) {
            return Lists.newArrayList();
        }
        // 遍历所有匹配的授权规则，收集其中的工具名称到一个集合中（去重）
        Set<String> authToolNameList = new HashSet<>();
        for (AuthorizationRule authorizationRule : appAuthRuleList) {
            authToolNameList.addAll(authorizationRule.getAuthToolList());
        }
        return new ArrayList<>(authToolNameList);
    }


}
