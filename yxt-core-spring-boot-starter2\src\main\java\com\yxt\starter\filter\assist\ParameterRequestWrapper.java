package com.yxt.starter.filter.assist;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Vector;

/**
 * HttpServletRequest 的包装类，代替 HttpServletRequest 向下传递
 *
 * <AUTHOR>
 * @date 2023年10月12日 16:15
 */

public class ParameterRequestWrapper extends HttpServletRequestWrapper {

    /**
     * 取代 HttpServletRequest 的 parameterMap
     */
    private Map<String, String[]> params = new HashMap<>();

    @SuppressWarnings("unchecked")
    public ParameterRequestWrapper(HttpServletRequest request) {
        // 将request交给父类，以便于调用对应方法的时候，将其输出，其实父亲类的实现方式和第一种new的方式类似
        super(request);
        // 将参数表，赋予给当前的Map以便于持有request中的参数
        this.params.putAll(request.getParameterMap());
    }

    public ParameterRequestWrapper(HttpServletRequest request, Map<String, Object> extendParams) {
        //重载一个构造方法
        this(request);
        addAllParameters(extendParams);//这里将扩展参数写入参数表
    }

    /**
     * 增加多个参数
     *
     * @param otherParams 参数map
     */
    public void addAllParameters(Map<String, Object> otherParams) {
        for (Map.Entry<String, Object> entry : otherParams.entrySet()) {
            addParameter(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 增加参数
     *
     * @param name 名称
     * @param value 值
     */
    public void addParameter(String name, Object value) {
        if (value != null) {
            if (value instanceof ArrayList) {
                String join = StringUtils.join((ArrayList<?>) value, ",");
                params.put(name, new String[]{ join });
            } else if (value instanceof String[]) {
                params.put(name, (String[]) value);
            } else if (value instanceof String) {
                params.put(name, new String[]{(String) value});
            } else {
                params.put(name, new String[]{String.valueOf(value)});
            }
        }
    }

    /**
     * 重写 getParameterNames，从当前类中的 map 获取
     * @return
     */
    @Override
    public Enumeration<String> getParameterNames() {
        Vector<String> vector = new Vector<String>(params.keySet());
        return vector.elements();
    }

    /**
     * 重写 getParameter，参数从当前类中的 map 获取
     *
     * @param name 键
     * @return 值
     */
    @Override
    public String getParameter(String name) {
        String[]values = params.get(name);
        if(values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    /**
     * 重写 getParameterValues方法，从当前类的 map中取值
     * @param name 键
     * @return 值
     */
    @Override
    public String[] getParameterValues(String name) {
        return params.get(name);
    }

    /**
     * 重写 getParameterMap，获取当前类的 map
     *
     * @return map
     */
    @Override
    public Map<String, String[]> getParameterMap() {
        return this.params;
    }
}

