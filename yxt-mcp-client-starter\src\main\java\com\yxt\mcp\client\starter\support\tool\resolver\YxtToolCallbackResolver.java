package com.yxt.mcp.client.starter.support.tool.resolver;

import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.resolution.ToolCallbackResolver;

/**
 * <AUTHOR>
 * @Description 覆盖默认的 ToolCallbackResolver 实现，
 * com.yxt.mcp.client.starter.entrance.observation.ChatClientObservationHandler
 * 根据用户权限做了过滤 不需要此逻辑了 覆盖
 * @Date 2025/06/18/18:37
 */
public class YxtToolCallbackResolver implements ToolCallbackResolver {

    @Override
    public ToolCallback resolve(String toolName) {
        return null;
    }
}
