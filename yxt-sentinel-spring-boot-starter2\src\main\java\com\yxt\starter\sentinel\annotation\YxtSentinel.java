package com.yxt.starter.sentinel.annotation;

import com.alibaba.csp.sentinel.EntryType;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于标识资源，自定义降级策略的注解标识
 *
 * <AUTHOR>
 * @Date 2024/8/27 15:39
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface YxtSentinel {

    /**
     * 是否上报资源
     *
     * @return
     */
    boolean shouldReportResource() default true;


    /**
     * 上报资源的名字,未传 如果需要上报则取方法名字
     *
     * @return
     */
    String value() default "";


    /**
     * 标识流量 入口和出口。默认是出口，只有协议入口才标识入口
     *
     * @return
     */
    EntryType entryType() default EntryType.OUT;

    /**
     * 降级类定义从容器获取 ，
     *
     * @return
     */
    Class<?> configFallbackClass() default void.class;

    /**
     * spring config类，做相关初始化操作,可配合configFallbackClass使用
     *
     * @return
     */
    Class<?>[] configuration() default {};


    /**
     * 表示流量的类型，默认为0表示web 1表示rpc
     *
     * @return
     */
    int resourceType() default 0;

    /**
     * 触发流控处理方法
     *
     * @return
     */
    String blockHandler() default "";

    /**
     * 触发流控处理类,配合blockHandler使用
     *
     * @return
     */
    Class<?>[] blockHandlerClass() default {};

    /**
     * 触发异常处理方法
     *
     * @return
     */
    String fallback() default "";

    /**
     * 默认异常处理方法
     *
     * @return
     */
    String defaultFallback() default "";

    /**
     * 触发异常处理类 ,配合fallback使用
     *
     * @return
     */
    Class<?>[] fallbackClass() default {};

    /**
     * 针对fallback,仅处理哪些异常
     *
     * @return
     */
    Class<? extends Throwable>[] exceptionsToTrace() default {Throwable.class};

    /**
     * 针对fallback,仅忽略哪些异常
     *
     * @return
     */
    Class<? extends Throwable>[] exceptionsToIgnore() default {};


}