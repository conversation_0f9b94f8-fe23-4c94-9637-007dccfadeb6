# 版本冲突检测与解决方案

## 概述
本文档详细分析了升级到JDK21和Spring Boot 3.5.0过程中可能出现的版本冲突，并提供了具体的解决方案。

## 主要版本冲突分析

### 1. SLF4J版本冲突
**冲突描述**: Spring Boot 3.x使用SLF4J 2.x，而老版本Log4j桥接器使用SLF4J 1.x
**影响组件**:
- log4j-slf4j-impl (SLF4J 1.x)
- spring-boot-starter-* (SLF4J 2.x)

**解决方案**:
```xml
<!-- 使用SLF4J 2.x兼容的桥接器 -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-slf4j2-impl</artifactId>
    <version>${log4j2.version}</version>
</dependency>
```

### 2. Jackson版本冲突
**冲突描述**: 多个组件引入不同版本的Jackson
**影响组件**:
- jackson-core-asl (老版本)
- spring-boot-starter-web (新版本)
- 各种第三方SDK

**解决方案**:
```xml
<!-- 移除老版本Jackson -->
<!-- jackson-core-asl已从依赖中移除 -->

<!-- 统一使用Spring Boot管理的Jackson版本 -->
<jackson.version>2.18.2</jackson.version>
```

### 3. FastJSON版本冲突
**冲突描述**: FastJSON 1.x与Spring Boot 3.x兼容性问题
**影响组件**:
- com.alibaba:fastjson
- 各种自定义starter中的fastjson依赖

**解决方案**:
```xml
<!-- 全局排除FastJSON 1.x -->
<exclusions>
    <exclusion>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
    </exclusion>
</exclusions>

<!-- 使用FastJSON2 -->
<dependency>
    <groupId>com.alibaba.fastjson2</groupId>
    <artifactId>fastjson2</artifactId>
    <version>${fastjson2.version}</version>
</dependency>
```

## 详细冲突解决方案

### 1. 日志框架统一
```xml
<properties>
    <!-- 统一Log4j2版本 -->
    <log4j2.version>2.24.3</log4j2.version>
</properties>

<!-- 核心日志组件 -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-api</artifactId>
    <version>${log4j2.version}</version>
</dependency>

<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-core</artifactId>
    <version>${log4j2.version}</version>
</dependency>

<!-- SLF4J 2.x桥接器 -->
<dependency>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-slf4j2-impl</artifactId>
    <version>${log4j2.version}</version>
</dependency>

<!-- 排除冲突的日志依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 2. Spring Cloud版本对齐
```xml
<properties>
    <!-- 确保版本对齐 -->
    <spring-boot.version>3.5.0</spring-boot.version>
    <spring-cloud.version>2024.0.0</spring-cloud.version>
    <spring-cloud-alibaba.version>2024.0.0.0</spring-cloud-alibaba.version>
</properties>

<!-- 版本对齐表 -->
<!-- Spring Boot 3.5.0 → Spring Cloud 2024.0.0 → Spring Cloud Alibaba 2024.0.0.0 -->
```

### 3. 监控组件版本对齐
```xml
<properties>
    <!-- Micrometer版本与Spring Boot对齐 -->
    <micrometer.version>1.14.2</micrometer.version>
</properties>

<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-core</artifactId>
    <version>${micrometer.version}</version>
</dependency>

<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
    <version>${micrometer.version}</version>
</dependency>
```

## 自定义组件兼容性处理

### 1. 自定义Starter兼容性
```xml
<!-- yxt-sentinel-spring-boot-starter -->
<dependency>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-sentinel-spring-boot-starter</artifactId>
    <exclusions>
        <!-- 排除FastJSON 1.x -->
        <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </exclusion>
    </exclusions>
</dependency>

<!-- seata-shardingsphere-spring-boot-starter -->
<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>seata-shardingsphere-spring-boot-starter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <exclusions>
        <!-- 排除FastJSON 1.x -->
        <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 2. 第三方组件兼容性
```xml
<!-- 阿里云OSS SDK -->
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>${aliyun-oss.version}</version>
    <exclusions>
        <!-- 排除可能的版本冲突 -->
        <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

## 版本冲突检测工具

### 1. Maven依赖分析
```bash
# 查看依赖树
mvn dependency:tree

# 查看冲突
mvn dependency:tree -Dverbose

# 分析特定依赖
mvn dependency:tree -Dincludes=org.apache.logging.log4j:*
```

### 2. IDEA插件检测
推荐使用以下IDEA插件：
- Maven Helper
- Dependency Analyzer

### 3. 自动化检测脚本
```bash
#!/bin/bash
# 检测常见版本冲突

echo "检测SLF4J版本冲突..."
mvn dependency:tree | grep slf4j

echo "检测Log4j版本冲突..."
mvn dependency:tree | grep log4j

echo "检测Jackson版本冲突..."
mvn dependency:tree | grep jackson

echo "检测FastJSON版本冲突..."
mvn dependency:tree | grep fastjson
```

## 运行时冲突解决

### 1. 类加载冲突
```java
// 检测类加载器
public class ClassLoaderChecker {
    public static void checkClassLoader() {
        System.out.println("SLF4J Logger: " + 
            org.slf4j.LoggerFactory.class.getClassLoader());
        System.out.println("Log4j2 Logger: " + 
            org.apache.logging.log4j.LogManager.class.getClassLoader());
    }
}
```

### 2. JVM参数优化
```bash
# 启动参数
-Dlog4j2.formatMsgNoLookups=true
-Dspring.profiles.active=prod
-Djava.awt.headless=true
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
```

## 测试验证方案

### 1. 单元测试
```java
@SpringBootTest
class DependencyCompatibilityTest {
    
    @Test
    void testLoggingFramework() {
        Logger logger = LoggerFactory.getLogger(this.getClass());
        logger.info("测试日志框架兼容性");
        // 验证日志正常输出
    }
    
    @Test
    void testJsonSerialization() {
        // 测试FastJSON2序列化
        String json = JSON.toJSONString(new TestObject());
        TestObject obj = JSON.parseObject(json, TestObject.class);
        assertNotNull(obj);
    }
}
```

### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class IntegrationTest {
    
    @Test
    void testApplicationStartup() {
        // 验证应用正常启动
    }
    
    @Test
    void testApiDocumentation() {
        // 验证SpringDoc正常工作
    }
}
```

## 持续监控方案

### 1. 依赖版本监控
```xml
<!-- Maven Versions Plugin -->
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>versions-maven-plugin</artifactId>
    <version>2.17.1</version>
    <configuration>
        <generateBackupPoms>false</generateBackupPoms>
    </configuration>
</plugin>
```

### 2. 自动化检测
```yaml
# GitHub Actions示例
name: Dependency Check
on: [push, pull_request]
jobs:
  dependency-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Check dependencies
        run: mvn dependency:analyze-report
```

## 回滚方案

### 1. 快速回滚
```bash
# 保存当前版本
cp pom.xml pom.xml.backup

# 回滚到原版本
cp pom.xml.original pom.xml
mvn clean install
```

### 2. 渐进式回滚
```xml
<!-- 分组回滚策略 -->
<!-- 1. 先回滚业务无关组件 -->
<!-- 2. 再回滚核心框架 -->
<!-- 3. 最后回滚日志系统 -->
```

## 总结

版本冲突解决的关键点：
1. **统一版本管理**: 在properties中集中管理版本
2. **排除冲突依赖**: 主动排除已知冲突的传递依赖
3. **版本对齐**: 确保相关组件版本兼容
4. **持续监控**: 建立自动化检测机制
5. **充分测试**: 验证升级后的功能完整性

通过以上方案，可以有效解决JDK21和Spring Boot 3.5.0升级过程中的版本冲突问题。