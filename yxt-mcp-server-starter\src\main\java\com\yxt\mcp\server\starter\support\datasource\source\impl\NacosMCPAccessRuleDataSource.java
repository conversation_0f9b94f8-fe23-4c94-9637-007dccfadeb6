package com.yxt.mcp.server.starter.support.datasource.source.impl;

import com.alibaba.fastjson2.TypeReference;
import com.yxt.mcp.server.starter.support.datasource.dto.AppAccessConfig;
import com.yxt.mcp.server.starter.support.datasource.listener.McpDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.source.McpAccessRuleDataSource;
import com.yxt.mcp.server.starter.support.properties.YxtNacosMcpProperties;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 * @Description Nacos数据源实现类
 * @Date 2025/06/17/11:07
 */
public class NacosMCPAccessRuleDataSource extends AbstractNacosDataSource<List<AppAccessConfig>> implements
    McpAccessRuleDataSource {

    public NacosMCPAccessRuleDataSource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpDataSourceListener> mcpDataSourceListenerList) {
        super(mcpDataSourceListenerList, yxtNacosMcpProperties.getServerAddr(), yxtNacosMcpProperties.getNamespace(),
            yxtNacosMcpProperties.getGroup(),
            yxtNacosMcpProperties.getAccessDataId(), yxtNacosMcpProperties.getTimeout(),
            new TypeReference<List<AppAccessConfig>>() {
            });
    }

    @Override
    public Optional<AppAccessConfig> getByAppId(String appId) {
        List<AppAccessConfig> appAccessConfigList = read();
        if (CollectionUtils.isEmpty(appAccessConfigList)) {
            return Optional.empty();
        }
        return appAccessConfigList.stream().filter(c -> Objects.equals(appId, c.getAppId())).findAny();
    }
}
