package com.yxt.mcp.server.starter.support.tool.register;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 自动查找MCP @Tool注解工具并注入，无需显式设置
 * @Date 2025/06/12/15:53
 */
@Component
public class AutoBeanToolRegisterTool implements BeanDefinitionRegistryPostProcessor {

    /**
     * 用于获取打了@TOOL注解的Bean
     */
    private final String yxtToolBeanNamesHolderBeanName = "YxtToolBeanNamesHolder";

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        String[] beanNames = registry.getBeanDefinitionNames();
        List<String> toolBeanNames = new ArrayList<>();

        for (String name : beanNames) {
            BeanDefinition bd = registry.getBeanDefinition(name);
            Class<?> beanClass = null;

            try {
                beanClass = Class.forName(bd.getBeanClassName());
            } catch (Throwable t) {
                continue;
            }
            // 扫描类的所有方法，检查是否存在 @Tool 注解
            boolean hasToolAnnotation = false;
            for (Method method : beanClass.getDeclaredMethods()) {
                if (AnnotationUtils.findAnnotation(method, Tool.class) != null) {
                    hasToolAnnotation = true;
                    break;
                }
            }
            if (hasToolAnnotation) {
                toolBeanNames.add(name);
            }
        }

        if (!registry.containsBeanDefinition(yxtToolBeanNamesHolderBeanName))
        // 注册一个 Holder Bean 存储匹配的 Bean 名称列表
        {
            registry.registerBeanDefinition(yxtToolBeanNamesHolderBeanName,
                new RootBeanDefinition(List.class, () -> toolBeanNames));
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        List<String> toolBeanNames = (List<String>) beanFactory.getBean(yxtToolBeanNamesHolderBeanName);

        if (CollectionUtils.isEmpty(toolBeanNames)) {
            return;
        }
        List<Object> toolBeans = new ArrayList<>();
        for (String name : toolBeanNames) {
            toolBeans.add(beanFactory.getBean(name));
        }

        ToolCallbackProvider provider = MethodToolCallbackProvider.builder()
            .toolObjects(toolBeans.toArray())
            .build();

        ((DefaultListableBeanFactory) beanFactory).registerSingleton("toolCallbackProvider", provider);
    }
}

