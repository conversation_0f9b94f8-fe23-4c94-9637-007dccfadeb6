package com.yxt.starter.configuration;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.info.InfoContributorProperties;
import org.springframework.boot.actuate.info.GitInfoContributor;
import org.springframework.boot.actuate.info.InfoPropertiesInfoContributor;
import org.springframework.boot.info.GitProperties;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.Ordered;

@Configuration
@Import({ApolloConfiguration.class,
    ServiceDiscoveryConfiguration.class,
    Knife4jConfiguration.class,
    DisLockConfiguration.class,
    DruidOnExcludeConfiguration.class,
    LogConfiguration.class
})
@Slf4j
@ServletComponentScan(basePackages = {"com.yxt.starter.filter"})
@ComponentScan(basePackages = {"com.yxt.starter.filter"})
public class YxtAutoConfiguration implements Ordered {

    @Override
    public int getOrder() {
        return Integer.MIN_VALUE;
    }


    @Autowired(required = false)
    public void setMetadata(
        NacosDiscoveryProperties nacosDiscoveryProperties,
//            ZookeeperDiscoveryProperties zookeeperDiscoveryProperties,
        GitProperties gitProperties,
        InfoContributorProperties infoContributorProperties,
        GitInfoContributor gitInfoContributor
    ) {
        /*
         * Set meta data from System Properties
         */
        if (nacosDiscoveryProperties != null
            && nacosDiscoveryProperties.getMetadata() != null) {
            Properties properties = System.getProperties();
            String prefix = "meta.";
            Map<String, String> meta = nacosDiscoveryProperties.getMetadata();
            for (Object obj : properties.keySet()) {
                if (!obj.toString().startsWith(prefix)) {
                    continue;
                }
                meta.put((String) obj, (String) properties.get(obj));
            }
        }

        /*
         *  Set meta data from Git
         */
        if (gitProperties != null
            && nacosDiscoveryProperties != null
            && nacosDiscoveryProperties.getMetadata() != null) {
            Map<String, String> meta = nacosDiscoveryProperties.getMetadata();
            meta.put("sys.start", new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
            meta.put("git.commit.id", gitProperties.getCommitId());
            meta.put("git.branch", gitProperties.getBranch());
            if (gitProperties.getCommitTime() != null) {
                meta.put("git.commit.time", gitProperties.getCommitTime().toString());
            }
            Instant buildTime = gitProperties.getInstant("build.time");
            if (buildTime != null) {
                meta.put("git.build.time", buildTime.toString());
            }
        }

        /*
         * Set git mode
         */
        if (infoContributorProperties != null && infoContributorProperties.getGit() != null) {
            infoContributorProperties.getGit().setMode(InfoPropertiesInfoContributor.Mode.FULL);
        }
        if (gitInfoContributor != null) {
            Field field = null;
            try {
                field = gitInfoContributor.getClass().getSuperclass().getDeclaredField("mode");
            } catch (NoSuchFieldException e) {
                log.error(e.getMessage(), e);
            }
            if (field != null) {
                field.setAccessible(true);
                try {
                    field.set(gitInfoContributor, InfoPropertiesInfoContributor.Mode.FULL);
                } catch (IllegalAccessException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }
}
