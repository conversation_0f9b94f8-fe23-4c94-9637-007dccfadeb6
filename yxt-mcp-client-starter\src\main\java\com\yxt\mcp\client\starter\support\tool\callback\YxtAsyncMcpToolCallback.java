package com.yxt.mcp.client.starter.support.tool.callback;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yxt.lang.dto.OperateContext;
import com.yxt.mcp.client.starter.support.constants.AuthFieldConstants;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.mcp.AsyncMcpToolCallback;

/**
 * <AUTHOR>
 * @Description 增强MCP工具调用传递用户信息
 * @Date 2025/06/11/13:42
 */
public class YxtAsyncMcpToolCallback extends AsyncMcpToolCallback {

    private static final Logger logger = LoggerFactory.getLogger(YxtAsyncMcpToolCallback.class);

    /**
     * 调用父类构造方法初始化工具回调对象。
     */
    public YxtAsyncMcpToolCallback(McpAsyncClient mcpClient,
        Tool tool) {
        super(mcpClient, tool);
    }

    /**
     * 重写call方法，增强 toolArguments 参数，插入业务场景和用户认证信息。
     *
     * @param toolArguments
     * @param toolContext
     * @return
     */
    @Override
    public String call(String toolArguments, ToolContext toolContext) {

        //liqiangtodo 检查是否存在有效的 ToolContext；
        if (Objects.nonNull(toolContext) && Objects.nonNull(toolContext.getContext())) {
            //获取身份信息 透传
            String business_scenario = Optional.ofNullable(
                    toolContext.getContext().get(AuthFieldConstants.BUSINESS_SCENARIO))
                .map(Object::toString).orElse(null);
            JSONObject toolArgumentsObj = JSON.parseObject(toolArguments);
            // 插入业务场景字段；
            toolArgumentsObj.put(AuthFieldConstants.BUSINESS_SCENARIO, business_scenario);
            // 调用 setAuthInfo(...) 添加用户认证信息；
            setAuthInfo(toolArgumentsObj, toolContext);
            toolArguments = JSON.toJSONString(toolArgumentsObj);
        }
        // 调用父类 call(toolArguments) 执行实际调用
        return call(toolArguments);
    }

    /**
     * 添加用户认证信息。
     *
     * @param toolArgumentsObj 工具参数
     * @param toolContext 工具上下文
     */
    private void setAuthInfo(JSONObject toolArgumentsObj, ToolContext toolContext) {
        Map<String, Object> contextAttr = toolContext.getContext();
        if (MapUtils.isEmpty(contextAttr)) {
            return;
        }
        for (Object value : contextAttr.values()) {
            // 遍历上下文值，找到第一个 OperateContext 实例进行填充；
            if (!(value instanceof OperateContext operateContext)) {
                continue;
            }
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID, operateContext.getOperatorId());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID,
                operateContext.getOperatorUserId());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN,
                operateContext.getOperatorLogin());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME,
                operateContext.getOperatorName());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE,
                operateContext.getOperatorSource());
            break;
        }
    }
}
