# 修复后的测试配置
dynamic:
  enable: true
  local-mappings:
    # 正则匹配 - 使用 (.+) 而不是 [(.+)]
    '(.+)': '$1.svc.k8s.test.hxyxt.com'
    
    # IP:端口格式（应该已经工作）
    'order-atom-service': '127.0.0.1:8080'
    
    # Nacos服务名
    'user-service': 'user-service-yrk'

# 启用调试日志
logging:
  level:
    com.yxt.dynamic.routing.proxy.LocalMappingFeignRequestInterceptor: DEBUG
    com.yxt.dynamic.routing: DEBUG

# 测试说明：
# 1. order-service 应该匹配正则 (.+) 并转换为 order-service.svc.k8s.test.hxyxt.com
# 2. order-atom-service 应该精确匹配并转换为 127.0.0.1:8080
# 3. user-service 应该精确匹配并通过Nacos发现转换为实际IP:端口
