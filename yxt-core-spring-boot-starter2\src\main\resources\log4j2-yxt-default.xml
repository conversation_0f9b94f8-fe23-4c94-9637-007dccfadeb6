<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <properties>
        <!--        <Property name="appName">MyApplication</Property>-->
        <Property name = "logPatternLayout">{ "@timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}{GMT+8}", "level": "%p", "traceId": "%replace{%traceId}{TID: }{}", "app": "${ctx:spring.application.name}", "thread": "%t", "method": "%c.%M:%L", "msg": "starter-----%yxtm%n", "stackTrace": "%ex"  }%n
        </Property>
    </properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${logPatternLayout}" />
        </Console>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console" />
        </Root>
    </Loggers>
</Configuration>
