package com.yxt.starter.metrics.register;

import com.yxt.starter.metrics.MetricsBinderRegister;
import com.yxt.starter.metrics.ext.binder.SentinelMetricsBinder;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.ApplicationContext;

public class SentinelMetricsBinderRegister implements MetricsBinderRegister {

    @Override
    public void registry(MeterRegistry meterRegistry, ApplicationContext context) {
        new SentinelMetricsBinder().bindTo(meterRegistry);
    }
}
