//package com.yxt.starter.metrics;
//
//import com.yxt.starter.metrics.register.DruidDataSourceMetricsBinderRegister;
//import com.yxt.starter.metrics.register.ExecutorMetricsBinderRegister;
//import com.yxt.starter.metrics.register.FeignClientMetricsBinderRegister;
//import com.yxt.starter.metrics.register.SentinelMetricsBinderRegister;
//import io.micrometer.core.instrument.MeterRegistry;
//import java.util.ArrayList;
//import java.util.List;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.ConfigurableApplicationContext;
//
// todo:依赖项目没升级暂时注释
//@Slf4j
//public class MetricsBinderRegisterLoader implements ApplicationListener<ApplicationReadyEvent> {
//
//    private static List<MetricsBinderRegister> registers = new ArrayList<>();
//
//    static {
//        try {
//            registers.add(new SentinelMetricsBinderRegister());
//            registers.add(new ExecutorMetricsBinderRegister());
//            registers.add(new DruidDataSourceMetricsBinderRegister());
//            registers.add(new FeignClientMetricsBinderRegister());
//        } catch (Throwable t) {
//            log.warn(t.getMessage(), t);
//        }
//    }
//
//    @Override
//    public void onApplicationEvent(ApplicationReadyEvent event) {
//        try {
//            load(event.getApplicationContext());
//        } catch (Throwable t) {
//            log.warn(t.getMessage(), t);
//        }
//    }
//
//    private void load(ConfigurableApplicationContext context) {
//        MeterRegistry meterRegistry = context.getBean(MeterRegistry.class);
//        for (MetricsBinderRegister register : registers) {
//            try {
//                register.registry(meterRegistry, context);
//            } catch (Throwable t) {
//                log.warn(t.getMessage(), t);
//            }
//        }
//    }
//}
