package com.yxt.mcp.server.starter.support.definition;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yxt.mcp.server.starter.support.constants.McpServerConstants;
import com.yxt.mcp.server.starter.support.constants.ToolProtocolEnum;
import com.yxt.mcp.server.starter.support.datasource.source.MCPToolDataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description 工具定义service
 * @Date 2025/06/12/18:35
 */
public class YxtToolDefinitionService {

    private static final Logger logger = LoggerFactory.getLogger(YxtToolDefinitionService.class);

    private final MCPToolDataSource mcpDataSource;

    public YxtToolDefinitionService(MCPToolDataSource mcpDataSource) {
        this.mcpDataSource = mcpDataSource;
    }

    public List<YxtToolDefinition> listYxtToolDefinition() {
        String configContent = mcpDataSource.read();
        if (configContent == null || configContent.isEmpty()) {
            return new ArrayList<>(); // 空回调数组
        }
        List<YxtToolDefinition> yxtToolDefinitions = new ArrayList<>();

        try {
            JSONArray toolConfigs = JSON.parseArray(configContent);

            for (int i = 0; i < toolConfigs.size(); i++) {
                JSONObject toolConfig = toolConfigs.getJSONObject(i);
                Optional<YxtToolDefinition> yxtToolDefinitionOptional = parse(toolConfig);
                yxtToolDefinitionOptional.ifPresent(yxtToolDefinitions::add);
            }
            return yxtToolDefinitions;
        } catch (Exception e) {
            logger.error("加载工具元数据失败={}", configContent, e);
            return yxtToolDefinitions;
        }
    }

    private Optional<YxtToolDefinition> parse(JSONObject toolConfig) {
        try {
            String toolName = toolConfig.getString(McpServerConstants.FIELD_TOOL_NAME);
            String toolDescription = toolConfig.getString(McpServerConstants.FIELD_TOOL_DESCRIPTION);
            JSONObject provider = toolConfig.getJSONObject(McpServerConstants.FIELD_PROVIDER);
            JSONObject protocolMeta = provider.getJSONObject(McpServerConstants.FIELD_PROTOCOL_META);
            String protocolStr = provider.getString(McpServerConstants.FIELD_PROTOCOL);
            ToolProtocolEnum protocol = ToolProtocolEnum.convert(protocolStr);
            YxtToolDefinition yxtToolDefinition = new YxtToolDefinition(toolName, toolDescription,
                toolConfig.toString(),
                protocol, protocolMeta);

            return Optional.of(yxtToolDefinition);
        } catch (Exception e) {
            logger.error("解析工具原始数据失败 parse configContent={}", toolConfig, e);
            return Optional.empty();
        }
    }
}
