local key = KEYS[1];
local lastTimeKey = KEYS[2];
local lastIndexKey = KEYS[3];
local limit = tonumber(ARGV[1]);
local slotCount = tonumber(ARGV[2]);
local slotMills = tonumber(ARGV[3]);
local currentTimeMills = tonumber(ARGV[4]);
local expiredSeconds = tonumber(ARGV[5]);

local hasKey = redis.call('EXISTS', key);
local hasLastTimeKey = redis.call('EXISTS', lastTimeKey);
local hasLastIndexKey = redis.call('EXISTS', lastIndexKey);
local lastIndex = redis.call('GET', lastIndexKey);

if hasKey == 1 and hasLastTimeKey == 1 and hasLastIndexKey == 1 then
    local lastTime = redis.call('GET', lastTimeKey);
    local diff = tonumber(currentTimeMills) - tonumber(lastTime);
    local newSlot = math.ceil(diff / slotMills);
    if newSlot > slotCount then
        newSlot = slotCount;
    end
    local maxIndex = slotCount - 1;
    for i = 0, newSlot do
        lastIndex = lastIndex + 1;
        if lastIndex > maxIndex then
            lastIndex = 0;
        end
        redis.call('HSET', key, lastIndex, 0);
    end

    redis.call('HINCRBY', key, lastIndex, 1);
    local sum = 0;
    local vals = redis.call('HVALS', key);
    for idx, v in ipairs(vals) do
        sum = sum + v;
    end
    if sum > limit then
        redis.call('HINCRBY', key, lastIndex, -1);
        redis.call('expire', key, expiredSeconds);
        redis.call('expire', lastTimeKey, expiredSeconds);
        redis.call('expire', lastIndexKey, expiredSeconds);
        return -1;
    end
else
    lastIndex = 0;
    for i = 0, slotCount
    do
        redis.call('HSET', key, i, 0);
    end
    redis.call('HINCRBY', key, lastIndex, 1);
end

redis.call('SET', lastIndexKey, lastIndex);
redis.call('SET', lastTimeKey, currentTimeMills);
redis.call('expire', key, expiredSeconds);
redis.call('expire', lastTimeKey, expiredSeconds);
redis.call('expire', lastIndexKey, expiredSeconds);
return 1;