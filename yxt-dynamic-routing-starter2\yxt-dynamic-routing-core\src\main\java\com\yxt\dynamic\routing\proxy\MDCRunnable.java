package com.yxt.dynamic.routing.proxy;

import org.slf4j.MDC;
/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 16:57
 */
public class MDCRunnable implements Runnable{
    private String routes;
    private Runnable runnable;

    public MDCRunnable(Runnable runnable) {
        this.routes = MDC.get(DynamicRouteContext.ROUTE_KEY);
        this.runnable = runnable;
    }

    @Override
    public void run() {
        if (routes != null) {
            MDC.put(DynamicRouteContext.ROUTE_KEY, routes);
        }
        try {
            runnable.run();
        } finally {
            MDC.clear();
        }
    }
}
