package com.yxt.dynamic.routing.agent.core;

import javassist.CannotCompileException;
import javassist.CtClass;
import javassist.CtField;
import javassist.NotFoundException;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/9 17:33
 */
public class JField {
    String fieldNameDes;
    JClass jClass;

    public JField(JClass jClass, String fieldNameDes) {
        this.jClass = jClass;
        this.fieldNameDes = fieldNameDes;
    }

    protected CtClass apply(CtClass ctClass) throws NotFoundException, CannotCompileException {
        CtField field = CtField.make(fieldNameDes, ctClass);
        ctClass.addField(field); // 设置字段的初始值
        return ctClass;
    }

    public JClass then() {
        return this.jClass;
    }
}
