package com.yxt.mcp.server.starter.support.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description 工具协议
 * @Date 2025/06/12/16:23
 */
@Getter
public enum ToolProtocolEnum {

    /**
     * 工具提供者协议cf
     */
    CONFLUENCE("confluence"),

    /**
     * 工具提供者jira
     */
    JIRA("jira"),
    /**
     * 微服务协议
     */
    SERVER("server");

    private final String protocol;

    ToolProtocolEnum(String protocol) {
        this.protocol = protocol;
    }

    public static ToolProtocolEnum convert(String protocol) {
        for (ToolProtocolEnum value : values()) {
            if (value.getProtocol().equalsIgnoreCase(protocol)) {
                return value;
            }
        }
        return null;
    }
}
