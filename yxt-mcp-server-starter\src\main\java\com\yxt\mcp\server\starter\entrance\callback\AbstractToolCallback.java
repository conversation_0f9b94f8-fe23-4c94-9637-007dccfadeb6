package com.yxt.mcp.server.starter.entrance.callback;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.support.constants.AuthFieldConstants;
import com.yxt.mcp.server.starter.support.constants.McpServerConstants;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import com.yxt.mcp.server.starter.support.utils.JsonFieldExtractor;
import com.yxt.mcp.server.starter.support.utils.SpringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description 统一处理的callback
 * @Date 2025/06/13/13:55
 */
@Slf4j
public abstract class AbstractToolCallback implements ToolCallback {

    private YxtToolDefinition toolDefinition;
    private ApplicationContext applicationContext;

    public AbstractToolCallback(final YxtToolDefinition toolDefinition, ApplicationContext applicationContext) {
        this.toolDefinition = toolDefinition;
        this.applicationContext = applicationContext;
    }

    /**
     * 返回当前回调对应的工具定义
     */
    @Override
    public ToolDefinition getToolDefinition() {
        return toolDefinition;
    }


    /**
     * 提供对内部 YxtToolDefinition 的访问。
     */
    public YxtToolDefinition getYxtToolDefinition() {
        return toolDefinition;
    }

    /**
     * 抽象方法，为空操作
     */
    @Override
    public String call(String toolInput) {
        return Strings.EMPTY;
    }

    public abstract String process(YxtAuthResult yxtAuthResult, String toolInput);

    /**
     * 核心的工具调用入口
     */
    @Override
    public String call(String toolInput, ToolContext tooContext) {
        YxtAuthResult yxtAuthResult = null;
        if (StringUtils.isNotBlank(toolInput)) {
            JSONObject arguments = JSON.parseObject(toolInput);
            String yxtEmpCode = arguments.getString(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID);
            String yxtUserId = arguments.getString(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID);
            String businessScenario = arguments.getString(AuthFieldConstants.BUSINESS_SCENARIO);
            String operatorLogin = arguments.getString(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN);
            String operatorName = arguments.getString(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME);
            String operatorSource = arguments.getString(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE);
            // 提取身份信息并构建 YxtAuthResult
            yxtAuthResult = new YxtAuthResult(yxtUserId, yxtEmpCode, businessScenario, operatorLogin, operatorName,
                    operatorSource);

            //身份信息是通过body透传，通过YxtAuthResult封装 从输入中移除敏感的身份字段，防止透传
            clearAuthData(arguments);
            toolInput = JSON.toJSONString(arguments);

        }
        try {
            String result = process(yxtAuthResult, toolInput);
            //根据配置中的 include_field_paths 字段路径，只返回指定字段的结果。
            Object includeFieldPaths = getYxtToolDefinition().getProtocolMeta()
                    .get(McpServerConstants.FIELD_PROTOCOL_META_SERVER_META_INCLUDE_FIELD_PATHS);
            if (Objects.isNull(includeFieldPaths) || !(includeFieldPaths instanceof JSONArray)) {
                return result;
            }
            List<String> includeFieldPathsList = ((JSONArray) includeFieldPaths).stream().map(Object::toString).collect(
                    Collectors.toList());
            return JsonFieldExtractor.extractFields(result, includeFieldPathsList);
        } catch (Exception e) {
            log.error("执行工具发生了异常toolInput={}", toolInput, e);
            return String.format("{\"error\": \"%s\"}", e.getMessage());
        }
    }

    /**
     * 解析 URL 模板并替换其中的占位符
     */
    protected String resolveUrlTemplate(String urlTemplate, String toolInput) {
        Map<String, Object> valuesMap = parseJsonToMap(toolInput);
        return StringSubstitutor.replace(urlTemplate, valuesMap);
    }

    /**
     * 从 Spring 容器中获取指定类型的 Bean
     */
    protected <T> T getBean(Class<T> clazz) {
        return SpringUtils.getBean(clazz);
    }

    /**
     * 将 JSON 字符串转换为 Map
     */
    private Map<String, Object> parseJsonToMap(String tooInput) {
        try {
            return JSON.parseObject(tooInput, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid toolInput JSON: " + tooInput, e);
        }
    }


    /**
     * 从请求参数中删除认证相关字段，避免敏感数据传递到下游系统。
     */
    private void clearAuthData(JSONObject arguments) {
        arguments.remove(AuthFieldConstants.APP_ID);
        arguments.remove(AuthFieldConstants.BUSINESS_SCENARIO);
        arguments.remove(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID);
        arguments.remove(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID);
        arguments.remove(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN);
        arguments.remove(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME);
        arguments.remove(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE);
        arguments.remove(AuthFieldConstants.TOKEN);
    }


}
