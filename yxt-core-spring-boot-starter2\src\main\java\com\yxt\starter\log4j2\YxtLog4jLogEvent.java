package com.yxt.starter.log4j2;

import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.Marker;
import org.apache.logging.log4j.ThreadContext;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.impl.Log4jLogEvent;
import org.apache.logging.log4j.message.Message;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-11-19 18:24
 * @description:
 */
public class YxtLog4jLogEvent extends Log4jLogEvent {

    private StackTraceElement[] stackTrace ;

    private Throwable yxtThrown;



    public StackTraceElement[] getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(StackTraceElement[] stackTrace) {
        this.stackTrace = stackTrace;
    }

    public YxtLog4jLogEvent(StackTraceElement[] stackTrace) {
        this.stackTrace = stackTrace;
    }

    public YxtLog4jLogEvent(long timestamp, StackTraceElement[] stackTrace) {
        super(timestamp);
        this.stackTrace = stackTrace;
    }

    public YxtLog4jLogEvent(String loggerName, Marker marker, String loggerFQCN, Level level, Message message, Throwable t) {
        super(loggerName, marker, loggerFQCN, level, message, t);
        if (t == null && level != null && level.intLevel() <= Level.WARN.intLevel()) {
            this.stackTrace = Thread.currentThread().getStackTrace();
        }
    }


    public YxtLog4jLogEvent(String loggerName, Marker marker, String loggerFQCN, Level level, Message message, Throwable t, Map<String, String> mdc, ThreadContext.ContextStack ndc, String threadName, StackTraceElement location, long timestampMillis, Thread thread) {
        super(loggerName, marker, loggerFQCN, level, message, t, mdc, ndc, threadName, location, timestampMillis);
        if (t == null && level != null && level.intLevel() <= Level.WARN.intLevel()) {
            this.stackTrace = Thread.currentThread().getStackTrace();
        }
    }


    public YxtLog4jLogEvent(
            final String loggerName,
            final Marker marker,
            final String loggerFQCN,
            final Level level,
            final Message message,
            final List<Property> properties,
            final Throwable t) {
        super(loggerName, marker, loggerFQCN, level, message, properties, t);
        if (t == null && level != null && level.intLevel() <= Level.WARN.intLevel()) {
            this.stackTrace = Thread.currentThread().getStackTrace();
        }

    }

    public YxtLog4jLogEvent(
            final String loggerName,
            final Marker marker,
            final String loggerFQCN,
            final StackTraceElement source,
            final Level level,
            final Message message,
            final List<Property> properties,
            final Throwable t) {
        super(loggerName, marker, loggerFQCN, source, level, message, properties, t);
        if (t == null && level != null && level.intLevel() <= Level.WARN.intLevel()) {
            this.stackTrace = Thread.currentThread().getStackTrace();
        }
    }


    @Override
    public Throwable getThrown() {
        if (super.getThrown() !=null){
            return super.getThrown();
        }
        return this.yxtThrown;
    }

    public void setThrown(Throwable thrown) {
        this.yxtThrown = thrown;
    }
}
