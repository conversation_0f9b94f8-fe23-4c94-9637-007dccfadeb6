package com.yxt.starter.configuration;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExprUtil;
import com.yxt.lang.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import redis.clients.jedis.JedisCluster;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Collections;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 *
 * Created by peng on 19/12/19.
 */
@Configuration
@ConditionalOnClass(RedisAutoConfiguration.class)
@ConditionalOnBean(RedisTemplate.class)
public class DisLockConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public DisLockAspect buildDisLock(RedisTemplate<String,String> stringRedisTemplate, RedisProperties redisProperties) {
        return new DisLockAspect(stringRedisTemplate, redisProperties);
    }

    @Aspect
    @Slf4j
    public static class DisLockAspect {

        private static final String KEY_PREFIX = "dis_lock";

        private static final int RANDOM_VALUE_BOUND = 100 * 1000 * 1000;

        private LockHandler optimisticLockHandler;

        private LockHandler pessimisticLockHandler;

        private Random random;

        DisLockAspect(RedisTemplate<String,String> stringRedisTemplate, RedisProperties redisProperties) {
            this.optimisticLockHandler = new OptimisticLockHandler(stringRedisTemplate, redisProperties);
            this.pessimisticLockHandler = new PessimisticLockHandler(stringRedisTemplate, redisProperties);
            this.random = new Random();
        }

        @Around("@annotation(disLock)")
        public Object around(ProceedingJoinPoint pjp, DisLock disLock) throws Throwable {
            Object result;
            LockHandler lockHandler = this.getLockHandler(disLock.lockType());
            String className = pjp.getTarget().getClass().getName();
            String methodName = pjp.getSignature().getName();
            String secondKeyPrefix = disLock.keyPrefix();
            if (StringUtils.isBlank(secondKeyPrefix)) {
                secondKeyPrefix = MD5Util.MD5Encode(className + "_" + methodName, "UTF-8");
            }
            String key = KEY_PREFIX + ":" + secondKeyPrefix + ":" + ExprUtil.parseEL(disLock.keyExpr(), ((MethodSignature)pjp.getSignature()).getMethod(), pjp.getArgs());
            String value = System.currentTimeMillis() + "" + this.random.nextInt(RANDOM_VALUE_BOUND);
            if (!lockHandler.tryLock(key, value, disLock.waitTime(), disLock.expireTime(), disLock.timeUnit())) {
                ResponseBase responseBase = ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
                if (StringUtils.isNotBlank(disLock.tipMessage())) {
                    responseBase.setMsg(disLock.tipMessage());
                }
                throw new YxtBizException("disLock! key is : " + key);
            }
            try {
                result = pjp.proceed(pjp.getArgs());
            } finally {
                if (!lockHandler.release(key, value)) {
                    //do nothing !
                }
            }
            return result;
        }

        private LockHandler getLockHandler(DisLock.LockType lockType) {
            return lockType == DisLock.LockType.OPTIMISTIC ? this.optimisticLockHandler : this.pessimisticLockHandler;
        }

    }

    @Retention(RetentionPolicy.RUNTIME)
    public @interface DisLock {

        /**
         * Key full pattern is: dis_lock:${the value of keyPrefix}:${the value of keyExpr}
         * @return The second prefix
         */
        String keyPrefix() default "";

        /**
         * The Spring Expression, Example:
         * <pre>
         *    &#64;DisLockConfiguration.DisLock(keyExpr = "'apply_' + #returnQuestReqDTO.orderId")
         *    public void method1(ReturnQuestReqDTO returnQuestReqDTO) {
         *        //do something...
         *    }
         * </pre>
         * @return expression
         */
        String keyExpr() default "";

        DisLock.LockType lockType() default DisLock.LockType.OPTIMISTIC;

        /**
         * <p>
         *     <li>only for "PESSIMISTIC", the wait time before try lock success. </li>
         * </p>
         */
        long waitTime() default 10 * 1000L;

        /**
         *  the key expiration timeout in redis
         */
        long expireTime() default 60 * 1000L;

        TimeUnit timeUnit() default TimeUnit.MILLISECONDS;

        String tipMessage() default "";

        enum LockType {
            PESSIMISTIC,
            OPTIMISTIC
        }
    }

    private static abstract class LockHandler {

        private static final String RELEASE_LOCK_LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

        private static final Long RELEASE_LOCK_SUCCESS_RESULT = 1L;

        RedisTemplate redisTemplate;

        RedisProperties redisProperties;

        LockHandler(RedisTemplate redisTemplate, RedisProperties redisProperties) {
            this.redisTemplate = redisTemplate;
            this.redisProperties = redisProperties;
        }

        abstract boolean tryLock(String key, String value, long waitTime, long expireTime, TimeUnit timeUnit);

        boolean release(String key, String value) {
            RedisScript<Long> redisScript = new DefaultRedisScript<>(RELEASE_LOCK_LUA_SCRIPT, Long.class);
            Long result = -1L;
            if (isJedisCluster(redisTemplate, redisProperties)) {
                result = (Long) redisTemplate.execute((RedisCallback) connection -> {
                    Object nativeConnection = connection.getNativeConnection();
                    if (nativeConnection instanceof JedisCluster) {
                        return ((JedisCluster) nativeConnection).eval(RELEASE_LOCK_LUA_SCRIPT, Collections.singletonList(key), Collections.singletonList(value));
                    }
                    return -1L;
                });
            } else {
                result = (Long) redisTemplate.execute(redisScript, Collections.singletonList(key), value);
            }
            return Objects.equals(result, RELEASE_LOCK_SUCCESS_RESULT);
        }
    }

    private static class OptimisticLockHandler extends LockHandler {

        OptimisticLockHandler(RedisTemplate<String,String> stringRedisTemplate, RedisProperties redisProperties) {
            super(stringRedisTemplate, redisProperties);
        }

        @Override
        public boolean tryLock(String key, String value, long waitTime, long expireTime, TimeUnit timeUnit) {
            return redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, timeUnit);
        }
    }

    @Slf4j
    private static class PessimisticLockHandler extends LockHandler {

        private static final long LEAST_WAIT = 100L;

        private Random random;

        PessimisticLockHandler(RedisTemplate<String, String> stringRedisTemplate, RedisProperties redisProperties) {
            super(stringRedisTemplate, redisProperties);
            this.random = new Random();
        }

        @Override
        public boolean tryLock(String key, String value, long waitTime, long expireTime, TimeUnit timeUnit) {
            long timeoutPopping = System.currentTimeMillis() + timeUnit.toMillis(waitTime);
            while (timeoutPopping > System.currentTimeMillis()) {
                if (redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, timeUnit)) {
                    return true;
                }
                try {
                    Thread.sleep(LEAST_WAIT + this.random.nextInt(300));
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                    // Restore interrupted state...
                    Thread.currentThread().interrupt();
                }
            }
            return false;
        }
    }

    public static boolean isJedisCluster(RedisTemplate redisTemplate, RedisProperties redisProperties) {
        return Objects.requireNonNull(redisTemplate.getConnectionFactory()).getClass().getSimpleName().equalsIgnoreCase("JedisConnectionFactory")
                && redisProperties.getSentinel() == null && redisProperties.getCluster() != null;
    }

}
