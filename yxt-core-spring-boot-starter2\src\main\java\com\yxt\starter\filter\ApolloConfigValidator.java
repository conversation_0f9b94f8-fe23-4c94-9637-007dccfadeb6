package com.yxt.starter.filter;

import com.ctrip.framework.apollo.internals.DefaultConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @date 2024/9/4 10:14
 * apollo配置参数格式校验
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ApolloConfigValidator implements InitializingBean {

    private final Environment env;

    @Override
    public void afterPropertiesSet() throws Exception{
        long begin = System.currentTimeMillis();
        if (env instanceof ConfigurableEnvironment) {
            ConfigurableEnvironment configurableEnv = (ConfigurableEnvironment) env;
            Iterator<PropertySource<?>> iterator = configurableEnv.getPropertySources().iterator();
            while (iterator.hasNext()) {
                PropertySource<?> ps = iterator.next();
                //apollo参数配置
                if (ps.getName().equals("ApolloBootstrapPropertySources")){
                    CompositePropertySource cps = (CompositePropertySource)ps;
                    Collection<PropertySource<?>> propertySources = cps.getPropertySources();
                    for (PropertySource<?> propertySource : propertySources) {
                        DefaultConfig config = (DefaultConfig) propertySource.getSource();
                        config.getPropertyNames().forEach(e-> validateConfig(propertySource.getName(), e));
                    }
                }
            }
        } else {
            throw new IllegalStateException("Expected a ConfigurableEnvironment");
        }
        long end = System.currentTimeMillis();
        log.info("ApolloConfigValidator 耗时:{}ms",end-begin);
    }

    public static void validateConfig(String namespace,String propertyName){
        if (namespace.equals("bizconfig.properties") && !propertyName.startsWith("biz.")){
            throw new RuntimeException("业务参数配置前缀错误，请以biz开头 参数名:"+propertyName);
        }
        if (namespace.equals("thirdconfig.properties") && !propertyName.startsWith("third.")){
            throw new RuntimeException("三方参数配置前缀错误，请以third开头 参数名:"+propertyName);
        }
    }
}
