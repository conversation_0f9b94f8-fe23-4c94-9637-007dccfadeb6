package com.yxt.mcp.server.starter.support.tool;

import com.google.common.collect.Lists;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinitionService;
import com.yxt.mcp.server.starter.support.tool.factory.ToolCallbackFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description 一心堂基于nacos的MCP工具初始化类
 * @Date 2025/06/12/16:42
 */
public class YxtNacosMcpToolsInitializer {

    private ApplicationContext applicationContext;
    private static final Logger logger = LoggerFactory.getLogger(YxtNacosMcpToolsInitializer.class);
    private final YxtToolDefinitionService yxtToolDefinitionService;

    public YxtNacosMcpToolsInitializer(YxtToolDefinitionService yxtToolDefinitionService,
        ApplicationContext applicationContext) {
        this.yxtToolDefinitionService = yxtToolDefinitionService;
        this.applicationContext = applicationContext;
    }

    public List<ToolCallback> initializeTools() {

        List<YxtToolDefinition> yxtToolDefinitions = yxtToolDefinitionService.listYxtToolDefinition();
        if (CollectionUtils.isEmpty(yxtToolDefinitions)) {
            return Lists.newArrayList();
        }

        List<ToolCallback> callbacks = new ArrayList<>();

        try {
            return yxtToolDefinitions.stream().map(c -> {
                return parseToolCallback(c, applicationContext);
            }).filter(Optional::isPresent).map(
                Optional::get).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("初始化工具失败 initializeTools configContent={}", yxtToolDefinitions, e);
            return callbacks;
        }

    }


    private Optional<ToolCallback> parseToolCallback(YxtToolDefinition yxtToolDefinition,
        ApplicationContext applicationContext) {
        try {

            ToolCallback toolCallback = ToolCallbackFactory.factory(yxtToolDefinition, applicationContext);
            return Optional.of(toolCallback);
        } catch (Exception e) {
            logger.error("初始化工具失败 parseToolCallback yxtToolDefinition={}", yxtToolDefinition, e);
            return Optional.empty();
        }
    }
}
