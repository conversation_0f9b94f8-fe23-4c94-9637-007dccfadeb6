package com.yxt.dynamic.routing.proxy;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;



/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 14:29
 */
public class DynamicProxyInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        DynamicRouteContext.setRoute(request.getHeader(DynamicRouteContext.ROUTE_KEY));
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        DynamicRouteContext.clear();
    }

}
