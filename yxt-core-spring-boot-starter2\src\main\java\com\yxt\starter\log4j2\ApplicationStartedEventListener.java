package com.yxt.starter.log4j2;

import com.yxt.common.alarm.util.ApolloConfigUtil;
import com.yxt.starter.filter.TraceIdFilter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.cloud.bootstrap.LoggingSystemShutdownListener;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.GenericApplicationListener;
import org.springframework.core.ResolvableType;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.util.StringUtils;

import static com.yxt.common.alarm.util.AlarmConst.CONF_APPLICATION_NAME;


@Slf4j
public class ApplicationStartedEventListener implements GenericApplicationListener {

    public static final String LOGS_USE_YXT_EVENT = "log4j2.useYxtEvent";
    private static Class<?>[] EVENT_TYPES = {ApplicationStartingEvent.class,
            ApplicationEnvironmentPreparedEvent.class, ApplicationPreparedEvent.class,
            ContextClosedEvent.class, ApplicationFailedEvent.class};
    private static final Class<?>[] SOURCE_TYPES = {SpringApplication.class,
            ApplicationContext.class};
    public static String SERVICE_NAME = "";

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        try {
            System.setProperty("org.springframework.boot.logging.LoggingSystem", "com.yxt.starter.log4j2.YxtLog4j2LoggingSystem");
            if (event instanceof ApplicationEnvironmentPreparedEvent) {

                if (StringUtils.isEmpty(SERVICE_NAME)) {
                    SERVICE_NAME = ApolloConfigUtil.getPropertyIgnoreError(CONF_APPLICATION_NAME);
                    setUseYxtEvent();
                    if (!StringUtils.isEmpty(SERVICE_NAME)) {
                        MDC.put(TraceIdFilter.SERVER_NAME, SERVICE_NAME);
                        return;
                    }

                }

                if (!StringUtils.isEmpty(SERVICE_NAME)) {
                    return;
                }
                ConfigurableEnvironment envi = ((ApplicationEnvironmentPreparedEvent) event).getEnvironment();
                MutablePropertySources mps = envi.getPropertySources();
                PropertySource<?> ps = mps.get("applicationConfig: [classpath:/application.yml]");
                if (ps != null) {
                    if (ps.containsProperty(CONF_APPLICATION_NAME)) {
                        SERVICE_NAME = (String) ps.getProperty(CONF_APPLICATION_NAME);
                        MDC.put(TraceIdFilter.SERVER_NAME, SERVICE_NAME);
                    }
                }
            }
        } catch (Exception e) {
            log.error("setUseYxtEvent error", e);
        }
    }

    /**
     * 获取apollo是否开启yxt自定义LogEvent
     */
    private static void setUseYxtEvent() {
        if (Boolean.FALSE.equals(YxtLogEventFactory.useYxtEvent)) {
            String propertyIgnoreError = ApolloConfigUtil.getPropertyIgnoreError(LOGS_USE_YXT_EVENT);
            if (!StringUtils.isEmpty(propertyIgnoreError) && Boolean.TRUE.equals(Boolean.valueOf(propertyIgnoreError))) {
                Boolean b = Boolean.valueOf(propertyIgnoreError);
                printInfo("useYxtEvent=" + b);
                YxtLogEventFactory.useYxtEvent = b;
            }
        }
    }

    public static void printInfo(String context) {
        System.out.println("\n**************************************" + context + " **************************************\n");
    }


    @Override
    public boolean supportsEventType(ResolvableType resolvableType) {
        return isAssignableFrom(resolvableType.getRawClass(), EVENT_TYPES);
    }

    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return isAssignableFrom(sourceType, SOURCE_TYPES);
    }

    private boolean isAssignableFrom(Class<?> type, Class<?>... supportedTypes) {
        if (type != null) {
            for (Class<?> supportedType : supportedTypes) {
                if (supportedType.isAssignableFrom(type)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public int getOrder() {
        //必须在 LoggingSystemShutdownListener在LoggingApplicationListener之前
        // LoggingSystemShutdownListener 之前的会跳过执行
        //要在LoggingApplicationListener 执行钱设置进去才生效
        // 大于Ordered.HIGHEST_PRECEDENCE + 5  小于 HIGHEST_PRECEDENCE + 20;;
        return LoggingSystemShutdownListener.DEFAULT_ORDER + 5;
    }
}
