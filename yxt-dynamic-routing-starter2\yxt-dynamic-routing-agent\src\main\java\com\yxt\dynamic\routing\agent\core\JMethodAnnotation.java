package com.yxt.dynamic.routing.agent.core;

import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.annotation.Annotation;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/21 18:01
 */
public class JMethodAnnotation {
    private String annName;

    private JMethod jMethod;


    public JMethodAnnotation(JMethod jMethod, String annName) {
        this.jMethod = jMethod;
        this.annName = annName;
    }

    public JMethod then() {
        return this.jMethod;
    }

    public JMethodAnnotation remove() {
        return this;
    }


    protected void removeClassApply(AnnotationsAttribute newAnnotationsAttr) {
        // 过滤掉特定注解
        for (Annotation annotation : newAnnotationsAttr.getAnnotations()) {
            if (annName.equals(annotation.getTypeName())) { // 替换为你要移除的注解的全限定名
                newAnnotationsAttr.removeAnnotation(annName);
            }
        }
    }

}