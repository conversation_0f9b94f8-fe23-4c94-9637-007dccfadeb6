package com.yxt.starter.sentinel.constants;

/**
 * 常量定义
 *
 * <AUTHOR>
 * @Date 2024/8/28 21:57
 */
public final class YxtSentinelConstants {

    private YxtSentinelConstants() {

    }

    /**
     * YxtSentinel容器名字
     */
    public static final String CONTEXT_NAME = "YxtSentinelContext";

    /**
     * yxtSentinel配置文件key
     */
    public static final String YXT_SENTINEL_CONFIG = "yxtSentinelConfig";

    /**
     * 加载yxtSentinel环境变量优先级 12 表示在applo配置加载之后
     */
    public static final int YXT_SENTINEL_ENVIRONMENT_PROCESS_ORDER = 12;


    /**
     * serviceName
     */
    public static final String HEAD_SERVICE_NAME_KEY = "service-name";

    /**
     * 资源名字
     */
    public static final String HEAD_RESOURCE_NAME_KEY = "resourceName";


    /**
     * yxt sentinel前缀
     */
    public static final String PROPERTY_PREFIX = "yxt.sentinel";

    /**
     * 授权配置兼容的告警配置
     */
    public static final String ALARM_TAG_KEY = "alarm.sendErrorLog.totag";
    /**
     * 授权配置兼容的告警配置
     */
    public static final String ALARM_USER_KEY = "alarm.sendErrorLog.touser";

    /**
     * 服务名字配置
     */
    public static final String APPLICATION_NAME_KEY = "spring.application.name";

    /**
     * 分隔符
     */
    public static final String SPLITTER = "\\|";

    /**
     * 授权属性名字
     */
    public static final String AUTH_FIELD_NAME = "auth";
}
