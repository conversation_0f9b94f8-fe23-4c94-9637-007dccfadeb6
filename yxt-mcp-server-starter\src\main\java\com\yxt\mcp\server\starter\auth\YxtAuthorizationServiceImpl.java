package com.yxt.mcp.server.starter.auth;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.lang.util.Conditions;
import com.yxt.mcp.server.starter.auth.listtool.ListToolAuthorizationHandler;
import com.yxt.mcp.server.starter.support.constants.AuthFieldConstants;
import com.yxt.mcp.server.starter.support.constants.AuthModeEnum;
import com.yxt.mcp.server.starter.support.datasource.dto.AppAccessConfig;
import com.yxt.mcp.server.starter.support.datasource.dto.AuthorizationRule;
import com.yxt.mcp.server.starter.support.datasource.dto.JWTInfo;
import com.yxt.mcp.server.starter.support.datasource.source.MCPAuthRuleDataSource;
import com.yxt.mcp.server.starter.support.datasource.source.McpAccessRuleDataSource;
import com.yxt.mcp.server.starter.support.properties.YxtAuthProperties;
import com.yxt.mcp.server.starter.support.utils.JWTHelper;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import jakarta.annotation.Resource;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description 授权相关接口, 提供了对用户权限、工具访问控制等操作的具体实现。
 * @Date 2025/06/13/14:59
 */
public class YxtAuthorizationServiceImpl implements AuthorizationService {


    private static final Logger logger = LoggerFactory.getLogger(YxtAuthorizationServiceImpl.class);

    @Resource
    private MCPAuthRuleDataSource mcpAuthRuleDataSource;

    @Resource
    private McpAccessRuleDataSource mcpAccessRuleDataSource;

    @Resource
    private List<ListToolAuthorizationHandler> listToolAuthorizationHandlerList;

    @Resource
    private YxtAuthProperties yxtAuthProperties;

    /**
     * 从调用参数中提取授权信息。
     * 如果认证模式为 INVOKE，则忽略传入的 yxtUserId 和 businessScenario。
     * 从 params 中提取 arguments 字段，并从中获取用户 ID、员工编码、业务场景、登录名、姓名、来源等字段。
     * 构造并返回 YxtAuthResult 对象
     */
    @Override
    public YxtAuthResult parseInvokeAuthResult(String appId, String yxtUserId, String yxtEmpCode,
                                               String businessScenario, Object params) {
        AuthModeEnum authModeEnum = getAuthModeByAppId(appId);
        String operatorLogin = null;
        String operatorName = null;
        String operatorSource = null;
        //针对调用传递，以调用参数为准
        if (AuthModeEnum.INVOKE == authModeEnum) {
            yxtUserId = null;
            businessScenario = null;
            if (params instanceof Map<?, ?> paramsMap) {
                LinkedHashMap<Object, Object> arguments = (LinkedHashMap) paramsMap.get("arguments");
                yxtUserId = Optional.ofNullable(arguments.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID))
                        .map(Object::toString)
                        .orElse(null);
                yxtEmpCode = Optional.ofNullable(arguments.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID))
                        .map(Object::toString)
                        .orElse(null);
                businessScenario = arguments.containsKey(AuthFieldConstants.BUSINESS_SCENARIO) ?
                        Optional.ofNullable(arguments.get(AuthFieldConstants.BUSINESS_SCENARIO))
                                .map(Object::toString)
                                .orElse(null) : null;
                operatorLogin = arguments.containsKey(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN) ?
                        Optional.ofNullable(arguments.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN))
                                .map(Object::toString).orElse(null) : null;

                operatorName = arguments.containsKey(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME) ?
                        Optional.ofNullable(arguments.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME))
                                .map(Object::toString).orElse(null) : null;

                operatorSource = arguments.containsKey(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE) ?
                        Optional.ofNullable(arguments.get(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE))
                                .map(Object::toString).orElse(null) : null;

            }
        }
        return new YxtAuthResult(yxtUserId, yxtEmpCode, businessScenario, operatorLogin, operatorName,
                operatorSource);
    }


    /**
     * 根据 appId 获取应用的认证模式。
     */
    public AuthModeEnum getAuthModeByAppId(String appId) {
        Conditions.checkNotBlank(appId, "appId不能为空");
        Optional<AppAccessConfig> appAccessConfigOpt = mcpAccessRuleDataSource.getByAppId(appId);
        Conditions.assertTrue(appAccessConfigOpt.isPresent(), "接入应用不存在");
        return appAccessConfigOpt.get().getAuthMode();
    }

    /**
     * 解析 YXT 授权结果。
     * INVOKE: 直接返回空
     * 如果认证模式为 SSE_USERID，则从传入的参数中获取用户 ID、员工编码、业务场景。
     * 如果认证模式为 SSE_JWT_TOKEN，则从传入的参数中获取 JWT token，并解析其中的用户 ID、员工编码、业务场景。
     * 构造并返回 YxtAuthResult 对象
     */
    @Override
    public Optional<YxtAuthResult> parseYxtAuthResult(String appId, String businessScenario, String userId,
                                                      String empCode,
                                                      String token) {

        if (StringUtils.isBlank(appId)) {
            return Optional.empty();
        }

        Optional<AppAccessConfig> appAccessConfigOpt = mcpAccessRuleDataSource.getByAppId(appId);

        if (appAccessConfigOpt.isEmpty()) {
            return Optional.empty();
        }
        AppAccessConfig appAccessConfig = appAccessConfigOpt.get();
        if (AuthModeEnum.INVOKE.equals(appAccessConfig.getAuthMode())) {
            return Optional.empty();
        } else if (AuthModeEnum.SSE_USERID.equals(appAccessConfig.getAuthMode())) {
            YxtAuthResult yxtAuthResult = new YxtAuthResult(userId, null, businessScenario, null, null, null);
            return Optional.of(yxtAuthResult);
        } else if (AuthModeEnum.SSE_JWT_TOKEN.equals(appAccessConfig.getAuthMode())) {
            try {
                if (StringUtils.isBlank(token)) {
                    return Optional.empty();
                }
                JWTInfo jwtInfo = JWTHelper.getInfoFromToken(token, yxtAuthProperties.getUserPubKey());
                return Optional.of(new YxtAuthResult(jwtInfo.getUserId(), jwtInfo.getEmpCode(), businessScenario, null,
                        jwtInfo.getZhName(), null));
            } catch (Exception e) {
                logger.error("解析token失败 token={}", token, e);
                return Optional.empty();
            }

        }
        return Optional.empty();
    }

    /**
     * 检查 SSE 权限。
     */
    @Override
    public boolean checkSSEPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult) {
        return checkPermission(appId, businessScenario, yxtAuthResult);
    }

    /**
     * 获取当前用户有权限的 tool 列表。
     */
    @Override
    public List<Tool> listToolsByPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult,
                                            List<Tool> toolList) {
        Set<String> allAuthToolList = listAuthToolNameList(appId, businessScenario, yxtAuthResult);
        if (CollectionUtils.isEmpty(allAuthToolList)) {
            return Lists.newArrayList();
        }
        return toolList.stream().filter(c -> allAuthToolList.contains(c.name())).collect(Collectors.toList());
    }

    /**
     * 异步检查是否具有调用指定工具的权限
     */
    @Override
    public Mono<Boolean> checkCallToolPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult,
                                                 String toolName) {
        if (StringUtils.isEmpty(toolName)) {
            return Mono.just(false);
        }
        checkPermission(appId, businessScenario, yxtAuthResult);
        Set<String> authToolList = listAuthToolNameList(appId, businessScenario, yxtAuthResult);
        return Mono.just(authToolList.contains(toolName));
    }

    /**
     * 获获取当前用户在特定业务场景下可访问的所有工具。
     */
    private Set<String> listAuthToolNameList(String appId, String businessScenario, YxtAuthResult yxtAuthResult) {
        checkPermission(appId, businessScenario, yxtAuthResult);
        List<AuthorizationRule> authorizationRules = mcpAuthRuleDataSource.read();
        if (CollectionUtils.isEmpty(authorizationRules)) {
            return Sets.newHashSet();
        }
        Set<String> allAuthToolList = new HashSet<>();
        // 遍历处理器
        for (ListToolAuthorizationHandler listToolAuthorizationHandler : listToolAuthorizationHandlerList) {
            // 执行 handle 方法获取每个处理器返回的工具权限列表
            List<String> authToolList = listToolAuthorizationHandler.handle(yxtAuthResult.yxtUserId(), appId,
                    businessScenario,
                    authorizationRules);
            if (CollectionUtils.isNotEmpty(authToolList)) {
                allAuthToolList.addAll(authToolList);
            }
        }
        return allAuthToolList;
    }

    /**
     * 检查基础权限。
     * 如果认证模式为 INVOKE，始终返回 true。
     * 如果为 SSE_USERID 或 SSE_JWT_TOKEN，则验证用户 ID 是否存在。
     * 若无权限，记录日志并返回 false。
     */
    private boolean checkPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult) {
        Conditions.checkNotBlank(appId, "appId不能为空");
        Optional<AppAccessConfig> appAccessConfigOptional = mcpAccessRuleDataSource.getByAppId(appId);
        Conditions.assertTrue(appAccessConfigOptional.isPresent(), "接入应用不存在");
        AppAccessConfig appAccessConfig = appAccessConfigOptional.get();
        if (AuthModeEnum.INVOKE.equals(appAccessConfig.getAuthMode())) {
            return true;
        } else if (AuthModeEnum.SSE_USERID.equals(appAccessConfig.getAuthMode()) || AuthModeEnum.SSE_JWT_TOKEN.equals(
                appAccessConfig.getAuthMode())) {
            boolean access = Objects.nonNull(yxtAuthResult) && StringUtils.isNotBlank(yxtAuthResult.yxtUserId());
            if (!access) {
                logger.warn("鉴权失败，userId不能为空 appId={},businessScenario={},userId={}", appId,
                        businessScenario, yxtAuthResult.yxtUserId());
            }
            return access;

        }
        return false;
    }


}
