<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yxt</groupId>
        <artifactId>yxt-xframe2</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yxt-xxljob-spring-boot-starter2</artifactId>
    <version>4.2.0</version>
    <properties>
        <xxl-job.version>2.4.1</xxl-job.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.9.4</version>
        </dependency>
    </dependencies>


    <distributionManagement>
        <repository>
            <id>local-releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.hxyxt.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>local-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>https://nexus.hxyxt.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>