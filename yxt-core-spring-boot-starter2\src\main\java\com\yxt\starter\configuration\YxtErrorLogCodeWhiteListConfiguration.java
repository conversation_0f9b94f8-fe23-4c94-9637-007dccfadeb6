package com.yxt.starter.configuration;

import com.yxt.starter.exception.whitelist.DefaultYxtErrorLogCodeWhiteListSupportImpl;
import com.yxt.starter.exception.whitelist.ErrorCodeAlarmWhiteCode;
import com.yxt.starter.exception.whitelist.YxtErrorLogCodeWhiteListHandler;
import com.yxt.starter.exception.whitelist.YxtErrorLogCodeWhiteListSupport;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 不告警error配置
 *
 * <AUTHOR>
 * @date 2024/01/22 11:48
 **/
@Configuration
@Import({DefaultYxtErrorLogCodeWhiteListSupportImpl.class, YxtErrorLogCodeWhiteListHandler.class,
    YxtErrorLogCodeWhiteListConfiguration.YxtErrorLogCodeWhiteListProperties.class})
public class YxtErrorLogCodeWhiteListConfiguration {


    @Bean("assistYxtErrorLogCodeWhiteListSupportImpl")
    @ConditionalOnBean(YxtErrorLogCodeWhiteListProperties.class)
    public YxtErrorLogCodeWhiteListSupport apolloConfig(YxtErrorLogCodeWhiteListProperties properties) {
        return properties::getWhiteList;
    }

    @Configuration
    @ConfigurationProperties(prefix = "yxt.error-log")
    public static class YxtErrorLogCodeWhiteListProperties {

        @Getter
        @Setter
        private List<ErrorCodeAlarmWhiteCode> whiteList;

    }
}
