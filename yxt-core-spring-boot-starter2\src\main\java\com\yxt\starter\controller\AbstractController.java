//package com.yxt.starter.controller;
//
//import cn.afterturn.easypoi.excel.ExcelExportUtil;
//import cn.afterturn.easypoi.excel.entity.ExportParams;
//import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
//
//import com.yxt.lang.dto.api.PageDTO;
//import com.yxt.lang.dto.api.ResponseBase;
//import com.yxt.lang.constants.response.ResponseCodeType;
//import com.yxt.lang.exception.YxtBizException;
//import com.yxt.lang.util.DateHelper;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.Data;
//import org.apache.poi.ss.usermodel.Workbook;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.validation.BindingResult;
//
//import java.io.BufferedOutputStream;
//import java.io.IOException;
//import java.io.OutputStream;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
// todo:不知道哪个服务在用是否保留
///**
// * 控制器抽象基类
// * <AUTHOR>
// * @version 1.0
// * @date 2019/7/24 10:43
// */
// todo:占时注释不知道具体用在什么地方
//@Data
//public abstract class AbstractController {
//    @Value("${api.version:1.0}")
//    private String API_VERSION;
//
//    @Value("${api.base-info-version:1.0}")
//    private String API_BASE_INFO_VERSION;
//
//    protected void checkValid(BindingResult result) {
//        if (result.hasErrors()) {
//            String message = result.getFieldError().getDefaultMessage();
//            throw new YxtBizException(message);
//        }
//    }
//
//    protected <T> ResponseBase<T> generateSuccess(T tObject) {
//        ResponseBase<T> base = ResponseBase.success();
//        base.setData(tObject);
//        return base;
//    }
//
//    protected <T> ResponseBase<T> generateObjectSuccess(T tObject) {
//        ResponseBase<T> base = ResponseBase.success();
//        base.setData(tObject);
//        return base;
//    }
//
//    /**
//     * 根据对数据库影响的行数，统一返回操作成功和失败
//     * <AUTHOR>
//     * @date 11:20 2020/04/10
//     * @param res 数据库影响行数
//     * @return com.yxt.starter.dto.ResponseBase
//     **/
//    protected ResponseBase generateBitSuccess(boolean res) {
//        if (res) {
//            return ResponseBase.success();
//        } else {
//            return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
//        }
//    }
//    /**
//     * 根据对数据库影响的行数，统一返回操作成功和失败
//     * <AUTHOR>
//     * @date 11:20 2019/11/29
//     * @param line 数据库影响行数
//     * @param size 目标数，为0，则line>0表示成功，不为0，line==size表示成功
//     * @return com.yxt.starter.dto.ResponseBase
//     **/
//    protected ResponseBase generateLineSuccess(int line,int size) {
//        if (size == 0) {
//            if (line > size) {
//                return ResponseBase.success();
//            } else {
//                return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
//            }
//        } else {
//            if (line == size) {
//                return ResponseBase.success();
//            } else {
//                return ResponseBase.fail(ResponseCodeType.BIZ_EXCEPTION);
//            }
//        }
//    }
//
//    protected <T> ResponseBase<PageDTO<T>> generatePageDtoSuccess(Long total,List<T> list) {
//        ResponseBase<PageDTO<T>> base = ResponseBase.success();
//        PageDTO<T> pageDTO = new PageDTO<T>();
//        pageDTO.setTotalCount(total);
//        pageDTO.setData(list);
//        base.setData(pageDTO);
//        return base;
//    }
//
//    protected <T> ResponseBase<T> generateError(ResponseCodeType type) {
//        return ResponseBase.fail(type);
//    }
//
//    /**
//     * EXCEL导出
//     * @param sheetName 表名
//     * @param list 列表
//     * @param tclass 类
//     * @param response 返回参
//     * @param <T>
//     * @return
//     */
//    protected <T> HttpServletResponse exportSheets(String sheetName, String fileName,List<T> list, Class<T> tclass, HttpServletResponse  response)  {
//
//        // 创建参数对象（用来设定excel得sheet得内容等信息）
//        ExportParams params1 = new ExportParams() ;
//        // 设置sheet得名称
//        params1.setSheetName(sheetName); ;
//        // 创建sheet1使用得map
//        Map<String,Object> dataMap1 = new HashMap<>();
//        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
//        dataMap1.put("title",params1) ;
//        // 模版导出对应得实体类型
//        dataMap1.put("entity", tclass) ;
//        // sheet中要填充得数据
//        dataMap1.put("data",list) ;
//        // 将sheet1和sheet2使用得map进行包装
//        List<Map<String, Object>> sheetsList = new ArrayList<>() ;
//        sheetsList.add(dataMap1);
//        // 执行方法
//        Workbook workbook = ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF) ;
//
//        // 获取workbook对象
//        // 判断数据
//        if(workbook == null) {
//            throw new YxtBizException();
//        }
//        // 重置响应对象
//        response.reset();
//        // 当前日期，用于导出文件名称
//        String dateStr = fileName+"_"+ DateHelper.getTodayString();
//        // 指定下载的文件名--设置响应头
//        response.setHeader("Content-Disposition", "attachment;filename=" + dateStr+".xls");
//        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
//        response.setHeader("Pragma", "no-cache");
//        response.setHeader("Cache-Control", "no-cache");
//        response.setDateHeader("Expires", 0);
//        // 写出数据输出流到页面
//        try {
//            OutputStream output = response.getOutputStream();
//            BufferedOutputStream bufferedOutPut = new BufferedOutputStream(output);
//            workbook.write(bufferedOutPut);
//            bufferedOutPut.flush();
//            bufferedOutPut.close();
//            output.close();
//        } catch (IOException e) {
//            throw new YxtBizException();
//        }
//        return response;
//    }
//}

