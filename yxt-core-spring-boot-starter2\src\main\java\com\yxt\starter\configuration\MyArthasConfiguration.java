package com.yxt.starter.configuration;


import com.alibaba.arthas.spring.ArthasConfiguration;
import com.alibaba.arthas.spring.ArthasProperties;
import com.alibaba.arthas.spring.StringUtils;
import com.taobao.arthas.agent.attach.ArthasAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;

@ConditionalOnProperty(name = "spring.arthas.enabled", matchIfMissing = true)
@EnableConfigurationProperties({ ArthasProperties.class })
@AutoConfigureBefore(value = {ArthasConfiguration.class})
@AutoConfigureOrder(value = -999)
public class MyArthasConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(ArthasConfiguration.class);

    @ConfigurationProperties(prefix = "arthas")
    @ConditionalOnMissingBean
    @Bean
    public HashMap<String, String> arthasConfigMap() {
        return new HashMap<>();
    }

    @ConditionalOnMissingBean
    @Bean
    public ArthasAgent arthasAgent(@Autowired Map<String, String> arthasConfigMap,
                                   @Autowired ArthasProperties arthasProperties) {
        try {
            arthasConfigMap = StringUtils.removeDashKey(arthasConfigMap);
            // 给配置全加上前缀
            Map<String, String> mapWithPrefix = new HashMap<>(arthasConfigMap.size());
            for (Map.Entry<String, String> entry : arthasConfigMap.entrySet()) {
                mapWithPrefix.put("arthas." + entry.getKey(), entry.getValue());
            }

            final ArthasAgent arthasAgent = new ArthasAgent(mapWithPrefix, arthasProperties.getHome(),
                    arthasProperties.isSlientInit(), null);

            arthasAgent.init();
            logger.info("Arthas agent start success.");
            return arthasAgent;
        } catch (Exception e) {
            return new ArthasAgent();
        }
    }
}
