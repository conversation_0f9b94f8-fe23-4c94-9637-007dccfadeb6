package com.yxt.starter.configuration;

import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.internals.DefaultConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

/**
 * 重试配置
 *
 * <AUTHOR>
 * @date 2023/10/26 20:01
 **/
@Configuration
public class RetryConfiguration {

    @ApolloConfig("application.yml")
    private DefaultConfig defaultConfig;

    @Value("${retry.maxAttempts:2}")
    private int maxAttempts;

    @Value("${retry.initialInterval:100}")
    private long initialInterval;

    @Value("${retry.multiplier:2}")
    private double multiplier;

    @Value("${retry.maxInterval:500}")
    private long maxInterval;
    private RetryTemplate retryTemplate;

    @Bean("yxtRetryTemplate")
    public RetryTemplate yxtRetryTemplate() {
        retryTemplate = new RetryTemplate();

        // 初始化重试策略和回退策略
        updateRetryTemplate();

        return retryTemplate;
    }

    @PostConstruct
    private void initialize() {
        //如果没有开启apollo，则不启用刷新
        if (defaultConfig != null) {
            defaultConfig.addChangeListener(changeListener);
        }
    }

    private void updateRetryTemplate() {
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(maxAttempts);

        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(initialInterval);
        backOffPolicy.setMultiplier(multiplier);
        backOffPolicy.setMaxInterval(maxInterval);

        retryTemplate.setRetryPolicy(retryPolicy);
        retryTemplate.setBackOffPolicy(backOffPolicy);
    }


    private final ConfigChangeListener changeListener = changeEvent -> {
        boolean retryConfigChanged = false;
        for (String changedKey : changeEvent.changedKeys()) {
            if (changedKey.startsWith("retry.")) {
                retryConfigChanged = true;
                break;
            }
        }

        if (retryConfigChanged) {
            maxAttempts = defaultConfig.getIntProperty("retry.maxAttempts", maxAttempts);
            initialInterval = defaultConfig.getLongProperty("retry.initialInterval", initialInterval);
            multiplier = defaultConfig.getDoubleProperty("retry.multiplier", multiplier);
            maxInterval = defaultConfig.getLongProperty("retry.maxInterval", maxInterval);
            updateRetryTemplate();
        }
    };
}
