package com.yxt.mcp.server.starter.support.tool.factory;

import com.yxt.mcp.server.starter.entrance.callback.JiraToolCallback;
import com.yxt.mcp.server.starter.entrance.callback.YxtConfluenceToolCallback;
import com.yxt.mcp.server.starter.entrance.callback.YxtServerToolCallback;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description 一心堂工具处理器创建工程
 * @Date 2025/06/12/16:24
 */
public class ToolCallbackFactory {

    public static ToolCallback factory(YxtToolDefinition definition, ApplicationContext applicationContext) {
        switch (definition.getProtocol()) {
            case CONFLUENCE:
                return new YxtConfluenceToolCallback(definition, applicationContext);
            case SERVER:
                return new YxtServerToolCallback(definition, applicationContext);
            case JIRA:
                return new Jira<PERSON>ool<PERSON>allback(definition, applicationContext);
            default:
                throw new IllegalArgumentException("Unsupported protocol: " + definition.getProtocol());
        }
    }
}
