# 动态路由本地配置示例
dynamic:
  enable: true
  local-mappings:
    # 正则匹配与替换 - 默认的svc地址
    '(.+)': '$1.svc.k8s.test.hxyxt.com'
    
    # IP加端口的形式
    'order-atom-service': '127.0.0.1:8080'
    
    # nacos服务名的形式
    'user-service': 'user-service-yrk'
    
    # 更复杂的正则匹配示例
    'order-(.+)-service': 'new-$1-service.svc.k8s.test.hxyxt.com'
    
    # 完整URL格式
    'payment-service': 'http://payment.internal.com:8080'

# 使用说明：
# 1. 精确匹配优先级最高
# 2. 如果没有精确匹配，会尝试正则表达式匹配
# 3. 支持三种目标格式：
#    - IP:端口格式 (如: 127.0.0.1:8080)
#    - 完整URL格式 (如: http://service.com:8080)
#    - 服务名格式 (如: service-name，会通过Nacos服务发现)
# 4. 正则表达式支持捕获组和替换 (如: $1, $2等)

# 转发示例：
# order-service -> order-service.svc.k8s.test.hxyxt.com (通过正则匹配)
# order-atom-service -> 127.0.0.1:8080 (通过精确匹配)
# user-service -> user-service-yrk的实际IP:端口 (通过Nacos服务发现)
