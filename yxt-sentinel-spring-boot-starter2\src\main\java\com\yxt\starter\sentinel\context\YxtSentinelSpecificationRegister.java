package com.yxt.starter.sentinel.context;

import com.yxt.starter.sentinel.annotation.YxtSentinel;
import com.yxt.starter.sentinel.constants.YxtSentinelConstants;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.MethodIntrospector;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.AnnotationUtils;

/**
 * 初始化@YxtSentinel 配置的config配置类
 *
 * <AUTHOR>
 * @Date 2024/8/28 15:51
 */
public class YxtSentinelSpecificationRegister implements BeanPostProcessor {


    private final List<YxtSentinelSpecification> yxtSentinelSpecificationList = new ArrayList<>();

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        registerClassYxtSentinelSpecification(bean);
        registerMethodYxtSentinelSpecification(bean);
        return bean;
    }

    public void registerClassYxtSentinelSpecification(Object bean) {
        YxtSentinel annotation = AnnotationUtils.getAnnotation(bean.getClass(), YxtSentinel.class);
        if (annotation == null) {
            return;
        }
        Class<?>[] configurationArr = annotation.configuration();
        if (configurationArr == null || configurationArr.length <= 0) {
            return;
        }
        YxtSentinelSpecification yxtSentinelSpecification = new YxtSentinelSpecification();
        yxtSentinelSpecification.setName(YxtSentinelConstants.CONTEXT_NAME);
        yxtSentinelSpecification.setConfiguration(configurationArr);
        yxtSentinelSpecificationList.add(yxtSentinelSpecification);
    }

    public void registerMethodYxtSentinelSpecification(Object bean) {

        Class<?> userType = bean.getClass();
        Map<Method, YxtSentinel> methods = MethodIntrospector.selectMethods(bean.getClass(),
            (MethodIntrospector.MetadataLookup<YxtSentinel>) method -> {
                try {
                    return getYxtSentinelForMethod(method);
                } catch (Throwable ex) {
                    throw new IllegalStateException("Invalid YxtSentinel on handler class [" +
                        userType.getName() + "]: " + method, ex);
                }
            });
        for (Entry<Method, YxtSentinel> methodYxtSentinelEntry : methods.entrySet()) {
            YxtSentinel yxtSentinel = methodYxtSentinelEntry.getValue();
            if (yxtSentinel != null && yxtSentinel.configuration() != null
                && yxtSentinel.configuration().length > 0) {
                YxtSentinelSpecification yxtSentinelSpecification = new YxtSentinelSpecification();
                yxtSentinelSpecification.setName("YxtSentinelContext");
                yxtSentinelSpecification.setConfiguration(yxtSentinel.configuration());
                yxtSentinelSpecificationList.add(yxtSentinelSpecification);
            }

        }

    }


    private YxtSentinel getYxtSentinelForMethod(Method method) {
        return AnnotatedElementUtils.findMergedAnnotation(method, YxtSentinel.class);

    }

    public List<YxtSentinelSpecification> getYxtSentinelSpecificationList() {
        return yxtSentinelSpecificationList;
    }
}
