package com.yxt.starter.xxljob.register;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import com.yxt.starter.xxljob.executor.XxlYxtCustomExecutor;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/23 14:53
 */
@Aspect
public class XXLShutdownAspect {

    @Value("${shutdown.xxl-job.sleep:3000}")
    private long shutdownSleep;

    private static final Logger log = LoggerFactory.getLogger(XXLShutdownAspect.class);

    private final XxlYxtCustomExecutor xxlYxtCustomExecutor;

    public XXLShutdownAspect(XxlYxtCustomExecutor xxlYxtCustomExecutor) {
        this.xxlYxtCustomExecutor = xxlYxtCustomExecutor;
    }

    @Before("execution(* org.springframework.boot.actuate.context.ShutdownEndpoint.shutdown())")
    public void runBeforeShutdownHook() {
        try {
            xxlYxtCustomExecutor.destroy();
            log.info("[下线xxl-job]");
            //给当前服务足够的时间处理已接受到的请求
            Thread.sleep(shutdownSleep);
        } catch (Exception ex) {
            log.error("[下线xxl-job] 失败 ==> " + ex.getMessage(), ex);
        }
    }
}
