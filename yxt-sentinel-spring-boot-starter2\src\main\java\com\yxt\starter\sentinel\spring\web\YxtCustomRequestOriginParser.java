package com.yxt.starter.sentinel.spring.web;

import com.alibaba.csp.sentinel.adapter.spring.webmvc_v6x.callback.RequestOriginParser;
import com.alibaba.csp.sentinel.adapter.spring.webmvc_v6x.config.SentinelWebMvcConfig;
import com.alibaba.csp.sentinel.adapter.web.common.UrlCleaner;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRule;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.yxt.starter.sentinel.constants.YxtSentinelConstants;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.HandlerMapping;

/**
 * 来源解析器
 *
 * <AUTHOR>
 * @Date 2024/09/26 13:56
 */
@Slf4j
public class YxtCustomRequestOriginParser implements RequestOriginParser {

    @Lazy
    @Resource
    private SentinelWebMvcConfig config;


    @Override
    public String parseOrigin(HttpServletRequest request) {
        Optional<String> originOpt = getOrigin(request);
        return originOpt.orElse(null);
    }


    /**
     * 获取来源标识 针对服务间调用有serviceName，针对外部调用则获取调用方ip
     *
     * @param request
     * @return
     */
    public Optional<String> getOrigin(HttpServletRequest request) {
        String origin = request.getHeader(YxtSentinelConstants.HEAD_SERVICE_NAME_KEY);
        if (StringUtils.isBlank(origin)) {
            return Optional.empty();
        }
        String resourceName = getResourceName(request);

        //只有配置了来源限流，做来源访问计数统计，防止统计规则占用大量内存
        boolean hasAuthorityRule = hasAuthorityRule(resourceName, origin);
        boolean hasFlowRule = hasFlowRule(resourceName, origin);
        if (!hasAuthorityRule && !hasFlowRule) {
            return Optional.empty();
        }
        return Optional.of(origin);
    }


    protected String getResourceName(HttpServletRequest request) {
        // 获取请求Url
        Object resourceNameObject = request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        if (resourceNameObject == null || !(resourceNameObject instanceof String)) {
            return null;
        }
        String resourceName = (String) resourceNameObject;
        //对Url做处理的扩展
        UrlCleaner urlCleaner = config.getUrlCleaner();
        if (urlCleaner != null) {
            resourceName = urlCleaner.clean(resourceName);
        }
        // 是否根据方法做区分
        if (StringUtils.isNotBlank(resourceName) && config.isHttpMethodSpecify()) {
            resourceName = request.getMethod().toUpperCase() + ":" + resourceName;
        }
        return resourceName;
    }


    private boolean hasAuthorityRule(String resourceName, String origin) {
        try {
            if (StringUtils.isBlank(origin)) {
                return false;
            }
            if (!AuthorityRuleManager.hasConfig(resourceName)) {
                return false;
            }
            List<AuthorityRule> authorityRuleList = AuthorityRuleManager.getRules();
            if (CollectionUtils.isEmpty(authorityRuleList)) {
                return false;
            }
            return authorityRuleList.stream().anyMatch(c -> origin.equals(c.getLimitApp()));
        } catch (Exception e) {
            log.error("#89 hasAuthorityRule error resourceName={}，origin={}", resourceName, origin, e);
        }
        return false;
    }

    private boolean hasFlowRule(String resourceName, String origin) {
        try {
            if (StringUtils.isBlank(origin)) {
                return false;
            }
            if (!FlowRuleManager.hasConfig(resourceName)) {
                return false;
            }
            List<FlowRule> flowRuleList = FlowRuleManager.getRules();
            if (CollectionUtils.isEmpty(flowRuleList)) {
                return false;
            }
            return flowRuleList.stream().anyMatch(c -> origin.equals(c.getLimitApp()));
        } catch (Exception e) {
            log.error("#118 hasFlowRule error resourceName={}，origin={}", resourceName, origin, e);
        }
        return false;
    }
}
