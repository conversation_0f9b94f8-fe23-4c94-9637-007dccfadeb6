package com.yxt.starter.exception;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.Tracer;
import com.alibaba.csp.sentinel.adapter.spring.webmvc_v6x.config.SentinelWebMvcConfig;
import com.alibaba.fastjson2.JSON;
import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.*;
import com.yxt.lang.util.ExLogger;
import com.yxt.starter.exception.whitelist.ErrorCodeAlarmWhiteCode;
import com.yxt.starter.exception.whitelist.YxtErrorLogCodeWhiteListHandler;
import com.yxt.starter.filter.TraceIdFilter;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@ControllerAdvice
@ResponseBody
public class DefaultExceptionHandlerImpl implements IExceptionHandler {

    static final String EXLOG_FIELD_0 = "error";

    @Resource
    private YxtErrorLogCodeWhiteListHandler whiteListHandler;

//    /**
//     * 自定义业务异常
//     *
//     * @param e
//     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
//     * <AUTHOR>
//     * @date 2023/10/24 16:18
//     */
//    @ExceptionHandler(value = YxtRuntimeException.class)
//    public Object handler(YxtRuntimeException e, HttpServletRequest request, HttpServletResponse response) {
//        sentinelTrace(request, e);
//
//        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
//        return logIt(request, response, responseBase, e);
//    }

    /**
     * 业务异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:18
     */
    @ExceptionHandler(value = YxtBizException.class)
    public Object handler(YxtBizException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 无权限异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:18
     */
    @ExceptionHandler(value = YxtNoPermissionException.class)
    public Object handler(YxtNoPermissionException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 未登录异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:18
     */
    @ExceptionHandler(value = YxtNotLoginException.class)
    public Object handler(YxtNotLoginException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 参数异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:18
     */
    @ExceptionHandler(value = YxtParamException.class)
    public Object handler(YxtParamException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 中间件异常
     *
     * @param e
     * @param request
     * @param response
     * @return
     */
    @ExceptionHandler(value = YxtFrameworkException.class)
    public Object handler(YxtFrameworkException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 系统异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:18
     */
    @ExceptionHandler(value = YxtSystemException.class)
    public Object handler(YxtSystemException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);

        ResponseBase responseBase = ResponseBase.fail(e.getCode(), e.getMessage(), e.getSubCode(), e.getSubMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 入参校验
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:19
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleBindException(MethodArgumentNotValidException e, HttpServletRequest request,
                                      HttpServletResponse response) {
        sentinelTrace(request, e);
        List<ObjectError> errorList = e.getBindingResult().getAllErrors();
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, Optional.of(errorList)
                .filter(errors -> !errors.isEmpty())
                .map(errors -> errors.get(0))
                .orElse(new ObjectError("unknown", "未知错误")).getDefaultMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 非法参数
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:20
     */
    @ExceptionHandler(value = IllegalArgumentException.class)
    public Object handler(IllegalArgumentException e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, e.getMessage());
        return logIt(request, response, responseBase, e);
    }

    /**
     * 自定义参数异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({ConstraintViolationException.class})
    public Object constraintViolationExceptionHandler(ConstraintViolationException exception, HttpServletRequest request, HttpServletResponse response) {
        final List<String> errorList = new ArrayList<>(exception.getConstraintViolations().size());
        exception.getConstraintViolations().forEach(item -> {
            errorList.add(item.getMessageTemplate());
        });
        sentinelTrace(request, exception);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, String.join("|", errorList));
        return logIt(request, response, responseBase, exception);
    }

    /**
     * 参数异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({BindException.class})
    public Object bindExceptionHandler(BindException exception, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, exception);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, Objects.requireNonNull(exception.getBindingResult().getFieldError()).getDefaultMessage());
        return logIt(request, response, responseBase, exception);
    }

    /**
     * 缺少请求参数异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({MissingServletRequestParameterException.class})
    public Object missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException exception, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, exception);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, "缺少必要的请求参数: " + exception.getParameterName());
        return logIt(request, response, responseBase, exception);
    }

    /**
     * 请求体读取异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler({HttpMessageNotReadableException.class})
    public Object httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException exception, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, exception);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.PARA_ERROR, "请求体内容无法正确读取，请检查格式");
        return logIt(request, response, responseBase, exception);
    }

    /**
     * 文件超大异常
     *
     * @param e
     * @return com.yxt.starter.dto.api.ResponseBase<java.lang.Void>
     * <AUTHOR>
     * @date 2023/10/24 16:21
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Object handleBindException(MaxUploadSizeExceededException e, HttpServletRequest request,
                                      HttpServletResponse response) {
        sentinelTrace(request, e);
        ResponseBase responseBase = buildResponseDTO(ResponseCodeType.SYS_EXCEPTION, "上传文件大小超过限制");
        return logIt(request, response, responseBase, e);
    }

    /**
     * For Other Exception
     */
    @ExceptionHandler(value = Exception.class)
    public ResponseBase handleOtherException(Exception e, HttpServletRequest request, HttpServletResponse response) {
        sentinelTrace(request, e);
        ResponseBase responseBase = buildResponseDTO();
        return logIt(request, response, responseBase, e);
    }

    private <T> T logIt(HttpServletRequest request, HttpServletResponse response, T dto, Exception e) {
        if (request == null || response == null || e == null) {
            return dto;
        }
        String uri = request.getRequestURI();
        String url = request.getRequestURL().toString();
        String message = e.getMessage();
        if (message == null) {
            message = JSON.toJSONString(dto);
        }
        ExLogger exLogger = ExLogger.logger().field(EXLOG_FIELD_0).field(uri).field(url).field(getDuration(request));
        if (e instanceof YxtBaseException) {
            if (this.checkWhite(e)) {
                exLogger.warn(message, e);
            } else {
                exLogger.error(message, e);
            }
        } else {
            exLogger.error(message, e);
        }
        try {
            if (dto instanceof ResponseBase) {
                return dto;
            }
            if (dto instanceof Map) {
                String msg = (String) ((Map) dto).get("msg");
                if (msg != null) {
                    ((Map) dto).put("msg", msg);
                }
                return dto;
            }
            Field field = dto.getClass().getField("msg");
            field.setAccessible(true);
            String msg = (String) field.get(dto);
            if (msg != null) {
                field.set(dto, msg);
            }
        } catch (Exception ex) {
            // ignore
        }
        return dto;
    }

    public static String getDuration(HttpServletRequest request) {
        long start = TraceIdFilter.getStartTimestamp(request);
        if (start < 0) {
            return String.valueOf(start);
        }
        return String.valueOf((System.currentTimeMillis() - start));
    }

    /**
     * 校验白名单
     *
     * @param e
     * @return
     */
    private Boolean checkWhite(Exception e) {
        List<ErrorCodeAlarmWhiteCode> whiteList = whiteListHandler.getWhiteList();

        // 一级code是否在白名单
        List<String> codeList = whiteList.stream().map(ErrorCodeAlarmWhiteCode::getCodeList).flatMap(List::stream)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        boolean codeWhite = Objects.nonNull(((YxtBaseException) e).getCode()) &&
                codeList.contains(((YxtBaseException) e).getCode());

        // 二级code是否在白名单
        List<String> subCodeList = whiteList.stream().map(ErrorCodeAlarmWhiteCode::getSubCodeList).flatMap(List::stream)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        boolean subCodeWhite = Objects.nonNull(((YxtBaseException) e).getSubCode()) &&
                subCodeList.contains(((YxtBaseException) e).getSubCode());

        // 模糊匹配msg是否在白名单
        PathMatcher antPathMatcher = new AntPathMatcher();
        List<String> msgBlurList = whiteList.stream().map(ErrorCodeAlarmWhiteCode::getMsgBlurList).flatMap(List::stream)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        boolean msgBlurWhite = Objects.nonNull(e.getMessage()) &&
                msgBlurList.stream().anyMatch(msgBlur -> antPathMatcher.match(msgBlur, e.getMessage()));

        return codeWhite || subCodeWhite || msgBlurWhite;
    }

    private void sentinelTrace(HttpServletRequest request, Throwable t) {
        if (t == null) {
            return;
        }
        Entry entry = (Entry) request.getAttribute(SentinelWebMvcConfig.DEFAULT_REQUEST_ATTRIBUTE_NAME);
        if (entry == null) {
            return;
        }
        Tracer.traceEntry(t, entry);
    }

    private ResponseBase buildResponseDTO() {
        return ResponseBase.fail(ResponseCodeType.SYS_EXCEPTION);
    }

    private ResponseBase buildResponseDTO(ResponseCodeType responseCodeType, String tipMessage) {
        ResponseBase responseBase = ResponseBase.fail(responseCodeType.getCode(), tipMessage);
        if (StringUtils.isNotBlank(tipMessage)) {
            responseBase.setMsg(tipMessage);
        }
        return responseBase;
    }
}

