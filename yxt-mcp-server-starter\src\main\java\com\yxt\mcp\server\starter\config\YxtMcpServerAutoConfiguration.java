package com.yxt.mcp.server.starter.config;

import com.yxt.mcp.server.starter.auth.AuthorizationService;
import com.yxt.mcp.server.starter.auth.YxtAuthorizationServiceImpl;
import com.yxt.mcp.server.starter.support.datasource.listener.McpDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.listener.McpToolDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.listener.impl.YxtMcpToolListener;
import com.yxt.mcp.server.starter.support.datasource.source.MCPAuthRuleDataSource;
import com.yxt.mcp.server.starter.support.datasource.source.MCPToolDataSource;
import com.yxt.mcp.server.starter.support.datasource.source.McpAccessRuleDataSource;
import com.yxt.mcp.server.starter.support.datasource.source.impl.NacosMCPAccessRuleDataSource;
import com.yxt.mcp.server.starter.support.datasource.source.impl.NacosMCPAuthRuleDatasource;
import com.yxt.mcp.server.starter.support.datasource.source.impl.NacosMCPToolDataSource;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinitionService;
import com.yxt.mcp.server.starter.support.properties.YxtAuthProperties;
import com.yxt.mcp.server.starter.support.properties.YxtNacosMcpProperties;
import com.yxt.mcp.server.starter.support.provider.YxtMcpAsyncToolsProvider;
import com.yxt.mcp.server.starter.support.provider.YxtMcpToolCallbackProvider;
import com.yxt.mcp.server.starter.support.provider.YxtToolsProvider;
import com.yxt.mcp.server.starter.support.tool.YxtNacosMcpToolsInitializer;
import io.modelcontextprotocol.server.McpAsyncServer;
import io.modelcontextprotocol.spec.McpServerSession;
import java.util.List;
import org.springframework.ai.mcp.server.autoconfigure.McpServerAutoConfiguration;
import org.springframework.ai.mcp.server.autoconfigure.McpServerProperties;
import org.springframework.ai.mcp.server.autoconfigure.McpWebFluxServerAutoConfiguration;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description 一心堂MCP server自动装配
 * @Date 2025/06/12/16:31
 */
@AutoConfiguration(after = {McpServerAutoConfiguration.class})
@EnableConfigurationProperties({YxtNacosMcpProperties.class, YxtAuthProperties.class})
@ConditionalOnProperty(prefix = McpServerProperties.CONFIG_PREFIX, name = "enabled", havingValue = "true",
    matchIfMissing = true)
@AutoConfigureAfter(McpWebFluxServerAutoConfiguration.class)
@Import(YxtWebFluxSseServerTransportProviderCustomConfig.class)
@ComponentScan(basePackages = {"com.yxt.mcp.server.starter.support", "com.yxt.mcp.server.starter.auth"})
@EnableDiscoveryClient // 启用服务发现
public class YxtMcpServerAutoConfiguration {

    /**
     * 一心堂工具管理器，创建工具提供者 Bean，使用 McpAsyncServer 提供异步工具调用能力。
     *
     * @param mcpAsyncServer
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(YxtToolsProvider.class)
    public YxtToolsProvider yxtToolsProvider(final McpAsyncServer mcpAsyncServer,
        McpServerSession.Factory sessionFactory, ApplicationContext applicationContext) {
        return new YxtMcpAsyncToolsProvider(mcpAsyncServer, sessionFactory, applicationContext);
    }

    /**
     * 创建工具数据源监听器，用于监听工具数据变化并刷新工具列表。
     * 使用 @Lazy 延迟加载依赖对象，避免启动时初始化顺序问题。
     */
    @Bean
    public McpToolDataSourceListener mcpDataSourceListener(@Lazy YxtToolsProvider yxtToolsProvider,
        @Lazy YxtToolDefinitionService yxtToolDefinitionService) {
        return new YxtMcpToolListener(yxtToolsProvider, yxtToolDefinitionService);
    }

    /**
     * 创建基于 Nacos 的工具数据源。
     * 支持从 Nacos 获取工具定义。
     * 支持注册监听器以响应数据变更
     */
    @Bean
    @ConditionalOnMissingBean(MCPToolDataSource.class)
    public MCPToolDataSource toolDataSource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpToolDataSourceListener> mcpToolDataSourceListenerList) {
        return new NacosMCPToolDataSource(yxtNacosMcpProperties, mcpToolDataSourceListenerList);
    }

    /**
     * 创建基于 Nacos 的权限数据源。
     */
    @Bean
    @ConditionalOnMissingBean(MCPAuthRuleDataSource.class)
    public MCPAuthRuleDataSource authDataSource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpDataSourceListener> mcpDataSourceListenerList) {
        return new NacosMCPAuthRuleDatasource(yxtNacosMcpProperties, mcpDataSourceListenerList);
    }

    /**
     * 创建基于 Nacos 的应用接入规则数据源。
     */
    @Bean
    @ConditionalOnMissingBean(McpAccessRuleDataSource.class)
    public McpAccessRuleDataSource accessDataSource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpDataSourceListener> mcpDataSourceListenerList) {
        return new NacosMCPAccessRuleDataSource(yxtNacosMcpProperties, mcpDataSourceListenerList);
    }

    /**
     * 创建工具定义服务，用于管理工具的元信息。
     */
    @Bean
    public YxtToolDefinitionService yxtToolDefinitionService(MCPToolDataSource MCPToolDataSource) {
        return new YxtToolDefinitionService(MCPToolDataSource);
    }

    /**
     * 初始化工具列表，从 Nacos 加载并注册到上下文中。
     */
    @Bean
    public YxtNacosMcpToolsInitializer yxtNacosMcpToolsInitializer(YxtToolDefinitionService yxtToolDefinitionService,
        ApplicationContext applicationContext) {
        return new YxtNacosMcpToolsInitializer(yxtToolDefinitionService, applicationContext);
    }

    /**
     * 创建工具回调提供者，用于动态注册工具回调逻辑。
     * org.springframework.ai.mcp.McpToolUtils#toSyncToolSpecification(org.springframework.ai.tool.ToolCallback,
     * org.springframework.util.MimeType)
     *
     * @param toolsInitializer
     * @return
     */
    @Bean
    public ToolCallbackProvider callbackProvider(final YxtNacosMcpToolsInitializer toolsInitializer) {
        return new YxtMcpToolCallbackProvider(toolsInitializer.initializeTools().toArray(new ToolCallback[0]));
    }

    /**
     * 创建授权服务 Bean。
     */
    @Bean
    @ConditionalOnMissingBean(AuthorizationService.class)
    public AuthorizationService yxtAuthorizationService() {
        return new YxtAuthorizationServiceImpl();
    }

    /**
     * 创建一个支持负载均衡的 RestTemplate。
     */
    @Bean
    @LoadBalanced // 启用Ribbon负载均衡
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
