package com.yxt.starter.filter;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "web-log-filter")
public class WebLogFilterConfig {

    /**
     * 参数打印总开关
     */
    private Boolean enable = false;

    /**
     * 请求参数打印是否开启
     */
    private Boolean requestEnable = false;

    /**
     * 请求参数哪些uri需要打印日志
     */
    private List<String> includeRequestPatternUris = new ArrayList<>();

    /**
     * 响应参数是否开启打印
     */
    private Boolean responseEnable = false;

    /**
     * 响应参数哪些uri需要打印日志
     */
    private List<String> includeResponsePatternUris = new ArrayList<>();

    /**
     * 打印长度，默认500个字符
     */
    private Integer printLogLength = 500;
}
