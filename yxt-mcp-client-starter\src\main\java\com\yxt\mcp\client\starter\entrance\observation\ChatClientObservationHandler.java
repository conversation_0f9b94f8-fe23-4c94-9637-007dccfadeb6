package com.yxt.mcp.client.starter.entrance.observation;

import com.alibaba.fastjson2.JSONObject;
import com.yxt.lang.dto.OperateContext;
import com.yxt.mcp.client.starter.support.constants.AuthFieldConstants;
import com.yxt.mcp.client.starter.support.provider.YxtAsyncMcpToolCallbackProvider;
import io.micrometer.observation.Observation.Context;
import io.micrometer.observation.ObservationHandler;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;
import org.springframework.ai.chat.client.observation.ChatClientObservationContext;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * //liqiangtodo  可用于会话全生命周期交互的扩展点？会话管理、提示词增强 等
 *
 * <AUTHOR>
 * @Description
 * org.springframework.ai.chat.client.DefaultChatClient.DefaultStreamResponseSpec#doGetObservableFluxChatResponse(org.springframework.ai.chat.client.ChatClientRequest)
 * @Date 2025/06/11/15:14
 */
@Component
public class ChatClientObservationHandler implements ObservationHandler<ChatClientObservationContext> {

    @Resource
    @Lazy
    private YxtAsyncMcpToolCallbackProvider yxtAsyncMcpToolCallbackProvider;

    /**
     * 判断当前处理器是否支持指定的上下文类型
     * @param context 观察上下文对象
     * @return 如果支持则返回 true，否则返回 false
     */
    @Override
    public boolean supportsContext(Context context) {
        return context instanceof ChatClientObservationContext;
    }

    /**
     * 在观察开始时注入认证信息到 OpenAI 的工具调用中
     * @param context
     */
    @Override
    public void onStart(ChatClientObservationContext context) {
        ChatOptions options = context.getRequest().prompt().getOptions();
        if (options instanceof OpenAiChatOptions) {
            OpenAiChatOptions openAiChatOptions = (OpenAiChatOptions) options;
            // 获取工具上下文
            Map<String, Object> toolContext = ((OpenAiChatOptions) options).getToolContext();
            JSONObject toolArgumentsObj = new JSONObject();
            JSONObject toolArguments = new JSONObject();
            toolArgumentsObj.put("arguments", toolArguments);
            // 设置认证信息到参数中
            setAuthInfo(toolArguments, toolContext);
            // 获取认证相关的工具回调
            ToolCallback[] authToolCallbacks = yxtAsyncMcpToolCallbackProvider.getAuthToolCallbacks(toolArgumentsObj);
            // 将认证工具回调添加到 OpenAI 聊天选项中
            openAiChatOptions.getToolCallbacks()
                .addAll(Arrays.asList(authToolCallbacks));
        }

        ObservationHandler.super.onStart(context);
    }

    /**
     * 设置认证信息到 JSON 对象中
     * @param toolArgumentsObj 存储参数的 JSON 对象
     * @param toolContext 工具上下文数据
     */
    private void setAuthInfo(JSONObject toolArgumentsObj, Map<String, Object> toolContext) {
        if (MapUtils.isEmpty(toolContext)) {
            return;
        }
        for (Object value : toolContext.values()) {
            if (!(value instanceof OperateContext operateContext)) {
                // 如果不是 OperateContext 类型则跳过
                continue;
            }
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_ID, operateContext.getOperatorId());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_OPERATOR_USER_ID,
                operateContext.getOperatorUserId());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_LOGIN,
                operateContext.getOperatorLogin());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_NAME,
                operateContext.getOperatorName());
            toolArgumentsObj.put(AuthFieldConstants.YXT_OPERATION_CONTEXT_SOURCE,
                operateContext.getOperatorSource());
            break;
        }
    }
}
