//package com.yxt.starter.configuration;
//
//import com.yxt.starter.metrics.MetricsBinderRegisterLoader;
//import com.yxt.starter.metrics.MyMetricsProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Import;
//
// todo:依赖项目没升级暂时注释
//@Configuration
//@Import(value = MyMetricsProperties.class)
//public class MyPrometheusConfiguration {
//
//    @Bean
//    public MetricsBinderRegisterLoader buildExecutorServiceMetrics() {
//        return new MetricsBinderRegisterLoader();
//    }
//}
