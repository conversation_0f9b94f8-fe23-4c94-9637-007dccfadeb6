package com.yxt.starter.configuration;

import com.yxt.lang.exception.IExceptionHandler;
import com.yxt.starter.exception.DefaultExceptionHandlerImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 异常注册配置
 *
 * <AUTHOR>
 * @date 2023/11/22 14:14
 **/
@Configuration
public class ExceptionConfiguration {

    @Bean()
    @ConditionalOnMissingBean(IExceptionHandler.class)
    public DefaultExceptionHandlerImpl buildExceptionHandler() {
        return new DefaultExceptionHandlerImpl();
    }
}
