package com.yxt.mcp.server.starter.config;

import com.yxt.mcp.server.starter.support.session.YxtSessionFactory;
import io.modelcontextprotocol.server.McpAsyncServer;
import io.modelcontextprotocol.spec.McpServerSession;
import io.modelcontextprotocol.spec.McpServerTransportProvider;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR>
 * @Description MCp初始化后做自定义配置
 * @Date 2025/06/16/11:21
 */
@Configuration
@DependsOn("mcpAsyncServer")
@Slf4j
public class YxtWebFluxSseServerTransportProviderCustomConfig {


    @Resource
    private McpAsyncServer mcpAsyncServer;

    /**
     * 作用：通过构造方法注入的方式自动装配所需的依赖对象。
     * 参数说明：
     * sessionFactory: 会话工厂，用于创建 McpServerSession。
     * mcpServerTransportProvider: 传输提供者，负责 HTTP 路由、事件处理等
     */
    @Autowired
    public void customize(McpServerSession.Factory sessionFactory,
                          McpServerTransportProvider mcpServerTransportProvider) {
        if ((sessionFactory instanceof YxtSessionFactory yxtSessionFactory)) {
            yxtSessionFactory.initializeHandlers(mcpAsyncServer);
        }
        mcpServerTransportProvider.setSessionFactory(sessionFactory);

    }

}
