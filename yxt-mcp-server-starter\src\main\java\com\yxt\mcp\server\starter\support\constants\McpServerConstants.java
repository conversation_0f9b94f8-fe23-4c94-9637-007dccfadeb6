package com.yxt.mcp.server.starter.support.constants;

/**
 * <AUTHOR>
 * @Description MCP服务常量
 * @Date 2025/06/12/17:17
 */
public final class McpServerConstants {

    private McpServerConstants() {

    }

    /**
     * 工具名字
     */
    public static final String FIELD_TOOL_NAME = "yxtToolName";
    /**
     * 工具描述
     */
    public static final String FIELD_TOOL_DESCRIPTION = "yxtToolDescription";
    /**
     * 协议
     */
    public static final String FIELD_PROTOCOL = "protocol";

    /**
     * 协议提供者
     */
    public static final String FIELD_PROVIDER = "yxtToolProvider";

    /**
     * 协议元数据
     */
    public static final String FIELD_PROTOCOL_META = "protocolMeta";

    /**
     * 协议地址
     */
    public static final String FIELD_PROTOCOL_META_URL = "url";

    /**
     * 服务名字
     */
    public static final String FIELD_PROTOCOL_META_SERVER_NAME = "serverName";

    public static final String FIELD_PROTOCOL_META_SERVER_URL = "serverUrl";

    /**
     * 需要提取的json字段
     */
    public static final String FIELD_PROTOCOL_META_SERVER_META_INCLUDE_FIELD_PATHS = "includeFieldPaths";


}
