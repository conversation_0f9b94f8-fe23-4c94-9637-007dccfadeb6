package com.yxt.mcp.server.starter.support.channel.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description MCP工具渠道配置
 * @Date 2025/06/13/9:55
 */
@Component
@ConfigurationProperties(prefix = MCPToolChannelProperties.CONFIG_PREFIX)
public class MCPToolChannelProperties {

    public static final String CONFIG_PREFIX = "spring.ai.yxt.channel";

    /**
     * cf渠道配置
     */
    private ConfluenceProperties confluence;

    /**
     * jira渠道
     */
    private JiraProperties jira;

    public JiraProperties getJira() {
        return jira;
    }

    public void setJira(JiraProperties jira) {
        this.jira = jira;
    }

    public ConfluenceProperties getConfluence() {
        return confluence;
    }

    public void setConfluence(ConfluenceProperties confluence) {
        this.confluence = confluence;
    }
}
