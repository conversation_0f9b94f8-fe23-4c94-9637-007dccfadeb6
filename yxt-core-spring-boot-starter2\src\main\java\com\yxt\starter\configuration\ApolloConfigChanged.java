package com.yxt.starter.configuration;

import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.List;
import java.util.Map;
import java.util.Properties;

@Slf4j
public class ApolloConfigChanged implements ApplicationContextAware {

    protected ApplicationContext applicationContext;
    private static final String LOG_PREFIX = "logging.level";

    @ApolloConfigChangeListener(value = {"application", "application.yml", "bizconfig", "hydee.common", "common"})
    protected void changeHandler(ConfigChangeEvent changeEvent) {
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            log.info("Apollo config change - {}", change.toString());
            if (key.startsWith(LOG_PREFIX) && PropertyChangeType.DELETED.equals(change.getChangeType())) {
                modifyLogLevel(key, changeEvent);
            }
        }
        this.applicationContext.publishEvent(new EnvironmentChangeEvent(changeEvent.changedKeys()));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private void modifyLogLevel(String key, ConfigChangeEvent changeEvent) {
        try {
            LoggerContext context = (LoggerContext) LogManager.getContext(false);
            if (key.length() <= LOG_PREFIX.length() + 1) {
                log.info("---------------ApolloConfigChanged----------------：key.length() <= LOG_PREFIX.length() + 1");
                return;
            }
            String packageName = key.substring(LOG_PREFIX.length() + 1);
            Configuration conf = context.getConfiguration();
            LoggerConfig rootLogger = conf.getRootLogger();
            Level rootLevel = rootLogger.getLevel();
            Level level = rootLevel == null ? Level.INFO : rootLevel;
            if ("root".equals(packageName)) {
                rootLogger.setLevel(level);
            } else {
                Map<String, LoggerConfig> loggers = conf.getLoggers();
                Level prefixLeve = getPrefixLevel(packageName, loggers);
                level = prefixLeve == null ? level : prefixLeve;

                loggers.get(packageName).setLevel(level);
            }
            context.updateLoggers(conf);
        } catch (Exception e) {
            log.error("ApolloConfigChanged error", e);
        }
    }

    /**
     * 递归查找包名前缀配置的日志级别
     *
     * @param packageName
     * @param loggers
     * @return
     */
    private Level getPrefixLevel(String packageName, Map<String, LoggerConfig> loggers) {
        if (org.springframework.util.StringUtils.isEmpty(packageName) || CollectionUtils.isEmpty(loggers)) {
            return null;
        }
        int i = packageName.lastIndexOf(".");
        if (i < 0) {
            return null;
        }
        String substring = packageName.substring(0, i);
        if (!loggers.containsKey(substring)) {
            return getPrefixLevel(substring, loggers);
        } else {
            return loggers.get(substring).getLevel();
        }
    }

    static {
        Method method = null;
        try {
            method = ApolloConfigChanged.class.getDeclaredMethod("changeHandler", ConfigChangeEvent.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        assert method != null;
        ApolloConfigChangeListener apolloConfigChangeListener = method.getAnnotation(ApolloConfigChangeListener.class);

        String nameSpaces = System.getProperty("apollo.bootstrap.namespaces");
        if (nameSpaces == null || "".equalsIgnoreCase(nameSpaces)) {
            InputStream inputStream = ApolloConfigChanged.class.getResourceAsStream("/bootstrap.properties");
            if (inputStream != null) {
                Properties properties = new Properties();
                try {
                    properties.load(inputStream);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                nameSpaces = properties.getProperty("apollo.bootstrap.namespaces");
            }
        }
        if (nameSpaces == null || "".equalsIgnoreCase(nameSpaces)) {
            InputStream inputStream = ApolloConfigChanged.class.getResourceAsStream("/bootstrap.yml");
            if (inputStream != null) {
                Yaml yaml = new Yaml();
                Map<String, Object> map = yaml.loadAs(inputStream, Map.class);
                if (map.get("apollo") != null) {
                    Map<String, Object> bootstrap = (Map<String, Object>) ((Map)map.get("apollo")).get("bootstrap");
                    if (bootstrap.get("namespaces") != null) {
                        Object ymlNameSpaces = bootstrap.get("namespaces");
                        if (ymlNameSpaces instanceof String) {
                            nameSpaces = (String) ymlNameSpaces;
                        }
                        if (ymlNameSpaces instanceof List) {
                            nameSpaces = StringUtils.join((List) ymlNameSpaces, ",");
                        }
                    }
                }
            }
        }

        if (nameSpaces != null) {
            try {
                InvocationHandler invocationHandler = Proxy.getInvocationHandler(apolloConfigChangeListener);
                Field value = invocationHandler.getClass().getDeclaredField("memberValues");
                value.setAccessible(true);
                Map<String, Object> memberValues = (Map<String, Object>) value.get(invocationHandler);
                memberValues.put("value", nameSpaces.split(","));
                log.warn("======= apolloConfigChangeListener: modified namespaces to: " + nameSpaces);
            } catch (IllegalAccessException | NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
    }
}