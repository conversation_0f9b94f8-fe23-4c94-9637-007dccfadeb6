/*
 * Copyright 2013-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yxt.starter.sentinel.fegin;

import com.alibaba.cloud.sentinel.feign.SentinelContractHolder;
import feign.Contract;
import feign.Feign;
import feign.InvocationHandlerFactory;
import feign.Target;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.FeignClientFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;


/**
 * 自定义feign Builder,sentinel兼容feign降级配置兼容
 *
 * <AUTHOR>
 * @Date 2024/8/17 10:09
 */
public final class YxtSentinelFeign {

    private YxtSentinelFeign() {

    }

    public static YxtSentinelFeign.Builder builder() {
        return new YxtSentinelFeign.Builder();
    }

    // 移除YxtSentinelFeign对Feign.Builder的继承。
    public static final class Builder implements ApplicationContextAware {

        private Contract contract = new Contract.Default();

        private ApplicationContext applicationContext;

        //FeignContext 在新版本的 Spring Cloud OpenFeign 中，相关功能已迁移到 org.springframework.cloud.openfeign.FeignClientFactory
        private FeignClientFactory feignClientFactory;

        // 新增：使用组合方式替代继承
        private final Feign.Builder feignBuilder = Feign.builder();

        public YxtSentinelFeign.Builder invocationHandlerFactory(
            InvocationHandlerFactory invocationHandlerFactory) {
            throw new UnsupportedOperationException();
        }

        public YxtSentinelFeign.Builder contract(Contract contract) {
            this.contract = contract;
            return this;
        }


        /**
         * 构建配置好的Feign.Builder实例
         * @return 配置好的Feign.Builder
         */
        public Feign.Builder buildBuilder() {
            if (applicationContext != null) {
                YxtSentinelFeignRequestInterceptor intercepter = applicationContext.getBean(
                    YxtSentinelFeignRequestInterceptor.class);
                feignBuilder.requestInterceptor(intercepter);
            }
            
            feignBuilder.invocationHandlerFactory(new InvocationHandlerFactory() {
                @Override
                public InvocationHandler create(Target target,
                    Map<Method, MethodHandler> dispatch) {
                    Class<?> fallback = null;
                    Class<?> fallbackFactory = null;
                    String beanName = null;
                    try {
                        Object feignClientFactoryBean = YxtSentinelFeign.Builder.this.applicationContext
                            .getBean("&" + target.type().getName());
                        fallback = (Class<?>) getFieldValue(feignClientFactoryBean,
                            "fallback");
                        fallbackFactory = (Class<?>) getFieldValue(feignClientFactoryBean,
                            "fallbackFactory");
                        beanName = (String) getFieldValue(feignClientFactoryBean,
                            "contextId");
                        if (!StringUtils.hasText(beanName)) {
                            beanName = (String) getFieldValue(feignClientFactoryBean, "name");
                        }
                    } catch (NoSuchBeanDefinitionException e) {//兼容类似角标那种非容器factoryBean方式构建feign代理方式
                        Class<?> targetClass = target.getClass();
                        FeignClient annotation = targetClass.getAnnotation(FeignClient.class);
                        if (Objects.nonNull(annotation)) {
                            fallback = annotation.fallback();
                            fallbackFactory = annotation.fallbackFactory();
                            // beanName = annotation.name();
                            // 优化
                            beanName = annotation.contextId();
                            if (!StringUtils.hasText(beanName)) {
                                beanName = annotation.name();
                            }
                        }
                    }

                    Object fallbackInstance;
                    FallbackFactory fallbackFactoryInstance;
                    // check fallback and fallbackFactory properties
                    if (void.class != fallback && fallback != null) {
                        fallbackInstance = getFromContext(beanName, "fallback", fallback,
                            target.type());
                        return new YxtSentinelInvocationHandler(target, dispatch,
                            new FallbackFactory.Default(fallbackInstance));
                    }
                    if (void.class != fallbackFactory && fallbackFactory != null) {
                        fallbackFactoryInstance = (FallbackFactory) getFromContext(
                            beanName, "fallbackFactory", fallbackFactory,
                            FallbackFactory.class);
                        return new YxtSentinelInvocationHandler(target, dispatch,
                            fallbackFactoryInstance);
                    }
                    return new YxtSentinelInvocationHandler(target, dispatch);
                }

                private Object getFromContext(String name, String type,
                    Class fallbackType, Class targetType) {
                    Object fallbackInstance = feignClientFactory.getInstance(name,
                        fallbackType);
                    if (fallbackInstance == null) {
                        throw new IllegalStateException(String.format(
                            "No %s instance of type %s found for feign client %s",
                            type, fallbackType, name));
                    }

                    if (!targetType.isAssignableFrom(fallbackType)) {
                        throw new IllegalStateException(String.format(
                            "Incompatible %s instance. Fallback/fallbackFactory of type %s is not assignable to %s for feign client %s",
                            type, fallbackType, targetType, name));
                    }
                    return fallbackInstance;
                }
            });

            feignBuilder.contract(new SentinelContractHolder(contract));
            return feignBuilder;
        }
        
        /**
         * 构建Feign实例
         * @return Feign实例
         */
        public Feign build() {
            return buildBuilder().build();
        }

        private Object getFieldValue(Object instance, String fieldName) {
            Field field = ReflectionUtils.findField(instance.getClass(), fieldName);
            if (field == null) {
                return null;
            }
            field.setAccessible(true);
            try {
                return field.get(instance);
            } catch (IllegalAccessException e) {
                // ignore
            }
            return null;
        }

        @Override
        public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
            this.applicationContext = applicationContext;
            feignClientFactory = this.applicationContext.getBean(FeignClientFactory.class);
        }

    }

}