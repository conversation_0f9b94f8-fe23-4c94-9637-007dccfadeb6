//package com.yxt.starter.metrics.register;
//
//import com.yxt.starter.metrics.MetricsBinderRegister;
//import com.alibaba.druid.pool.DruidDataSource;
//import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
//import io.micrometer.core.instrument.MeterRegistry;
//import io.micrometer.core.instrument.Tags;
//import io.seata.rm.datasource.AbstractDataSourceProxy;
//import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
//import org.springframework.boot.actuate.metrics.jdbc.DataSourcePoolMetrics;
//import org.springframework.boot.jdbc.metadata.AbstractDataSourcePoolMetadata;
//import org.springframework.context.ApplicationContext;
//
//import javax.sql.DataSource;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.stream.Collectors;
//
// todo:依赖项目没升级暂时注释
//public class DruidDataSourceMetricsBinderRegister implements MetricsBinderRegister {
//
//    @Override
//    public void registry(MeterRegistry meterRegistry, ApplicationContext context) {
//        try {
//            Map<String, DruidDataSource> dataSourceMap = context.getBeansOfType(DruidDataSource.class);
//            if (dataSourceMap.isEmpty()) {
//                dataSourceMap = fromDynamicDatasource(context);
//            }
//            if (dataSourceMap.isEmpty()) {
//                dataSourceMap = fromShardingSphere(context);
//            }
//            dataSourceMap.forEach((key, value) -> new DataSourcePoolMetrics(value, dataSource -> new DruidDataSourcePoolMetadata((DruidDataSource) dataSource), key, Tags.empty()).bindTo(meterRegistry));
//        } catch (Throwable e) {
//            //ignore
//        }
//    }
//
//    private Map<String, DruidDataSource> fromShardingSphere(ApplicationContext context) {
//        try {
//            ShardingDataSource shardingDataSource = context.getBean(ShardingDataSource.class);
//            return convertDataSourceMap(shardingDataSource.getDataSourceMap());
//        } catch (Throwable t) {
//            //ignore
//        }
//        return new HashMap<>();
//    }
//
//    private Map<String, DruidDataSource> fromDynamicDatasource(ApplicationContext context) {
//        try {
//            DynamicRoutingDataSource dynamicRoutingDataSource = context.getBean(DynamicRoutingDataSource.class);
//            return convertDataSourceMap(dynamicRoutingDataSource.getDataSources());
//        } catch (Throwable e) {
//            //ignore
//        }
//        return new HashMap<>();
//    }
//
//    private Map<String, DruidDataSource> convertDataSourceMap(Map<String, DataSource> dataSourceMap) {
//        return dataSourceMap.entrySet().stream().collect(
//                Collectors.toMap(Map.Entry::getKey, entry -> {
//                    DataSource dataSource = entry.getValue();
//                    if (dataSource instanceof DruidDataSource) {
//                        return (DruidDataSource) dataSource;
//                    }
//                    if (dataSource instanceof AbstractDataSourceProxy) {
//                        dataSource = ((AbstractDataSourceProxy) dataSource).getTargetDataSource();
//                    }
//                    return (DruidDataSource) dataSource;
//                }));
//    }
//
//    private static class DruidDataSourcePoolMetadata extends AbstractDataSourcePoolMetadata<DruidDataSource> {
//
//        /**
//         * Create an instance with the data source to use.
//         *
//         * @param dataSource the data source
//         */
//        DruidDataSourcePoolMetadata(DruidDataSource dataSource) {
//            super(dataSource);
//        }
//
//        @Override
//        public Integer getActive() {
//            return getDataSource().getActiveCount();
//        }
//
//        @Override
//        public Integer getMax() {
//            return getDataSource().getMaxActive();
//        }
//
//        @Override
//        public Integer getMin() {
//            return getDataSource().getMinIdle();
//        }
//
//        @Override
//        public String getValidationQuery() {
//            return getDataSource().getValidationQuery();
//        }
//
//        @Override
//        public Boolean getDefaultAutoCommit() {
//            return getDataSource().isDefaultAutoCommit();
//        }
//    }
//}
