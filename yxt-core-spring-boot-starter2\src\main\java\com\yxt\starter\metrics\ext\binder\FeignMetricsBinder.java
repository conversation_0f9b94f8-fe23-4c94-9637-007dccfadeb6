package com.yxt.starter.metrics.ext.binder;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.MeterBinder;
import io.micrometer.core.lang.NonNull;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

/**
 * <AUTHOR>
 * @date 2025/03/10 18:07
 **/
public class FeignMetricsBinder implements MeterBinder {

    private final OkHttpClient okHttpClient;
    private final Tags tags;

    // 标签常量，标识该连接池是feign连接
    private static final Tag CUSTOM_TAG = Tag.of("monitor_source", "feign_custom");

    public FeignMetricsBinder(OkHttpClient okHttpClient, Tags tags) {
        this.okHttpClient = okHttpClient;
        this.tags = tags;
    }

    @Override
    public void bindTo(@NonNull MeterRegistry registry) {
        ConnectionPool pool = okHttpClient.connectionPool();

        // 活跃连接数
        Gauge.builder("feign.connections.total", pool, ConnectionPool::connectionCount)
            .tags(tags.and(CUSTOM_TAG))
            .description("Current all connections managed by Feign's OkHttpClient")
            .register(registry).measure();

        // 空闲连接数
        Gauge.builder("feign.connections.idle", pool, ConnectionPool::idleConnectionCount)
            .tags(tags.and(CUSTOM_TAG))
            .description("Idle connections ready for reuse")
            .register(registry).measure();
    }
}
