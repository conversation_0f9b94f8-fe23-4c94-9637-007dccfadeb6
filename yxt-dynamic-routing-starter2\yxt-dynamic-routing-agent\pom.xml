<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <groupId>com.yxt.dynamic.routing</groupId>
    <artifactId>yxt-dynamic-routing-agent</artifactId>
    <version>${dynamic.routing}</version>

    <packaging>jar</packaging>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <module.deploy.skip>false</module.deploy.skip>
        <dynamic.routing>2.0.1-SNAPSHOT</dynamic.routing>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.30.2-GA</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>yxt-dynamic-routing-agent</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.4.1</version>
                <configuration>
                    <!-- get all project dependencies -->
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <!-- MainClass in mainfest make a executable jar -->
                    <archive>
                        <!--自动添加META-INF/MANIFEST.MF -->
                        <manifest>
                            <addClasspath>true</addClasspath>
                        </manifest>
                        <manifestEntries>
                            <!--permain方法所在类的完全限定名-->
                            <Premain-Class>com.yxt.dynamic.routing.agent.Main</Premain-Class>
                            <Agent-Class>com.yxt.dynamic.routing.agent.Main</Agent-Class>
                            <Can-Redefine-Classes>true</Can-Redefine-Classes>
                            <Can-Retransform-Classes>true</Can-Retransform-Classes>
                        </manifestEntries>
                    </archive>
                </configuration>
                <executions>
                    <!-- 配置执行器 -->
                    <execution>
                        <id>make-assembly</id>
                        <!-- 绑定到package命令的生命周期上 -->
                        <phase>package</phase>
                        <goals>
                            <!-- 只运行一次 -->
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>

        <repository>
            <id>hydee</id>
            <name>hydee</name>
            <url>http://nexus.hxyxt.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>local-releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.hxyxt.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>local-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>https://nexus.hxyxt.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>