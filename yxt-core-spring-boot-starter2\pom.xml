<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>yxt-xframe2</artifactId>
        <groupId>com.yxt</groupId>
        <version>2.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-core-spring-boot-starter2</artifactId>
    <version>2.0.0</version>

    <properties>
        <disruptor.version>4.0.0</disruptor.version>

        <!-- 监控相关版本 -->
        <micrometer.version>1.14.2</micrometer.version>
        <arthas.version>3.7.2</arthas.version>

        <!-- 工具类版本 -->
        <janino.version>3.1.12</janino.version>

        <!-- 云存储版本 -->
        <qiniu.version>7.16.0</qiniu.version>
        <aliyun-oss.version>3.19.1</aliyun-oss.version>

        <!-- Excel处理版本 -->
        <easyexcel.version>4.0.3</easyexcel.version>

        <!-- 数据库相关版本 -->
        <jedis.version>5.2.0</jedis.version>

        <!-- 安全相关版本 -->
        <bouncycastle.version>1.79</bouncycastle.version>

        <!-- 配置中心版本 -->
        <apollo.version>2.3.0</apollo.version>

        <!-- 其他工具版本 -->
        <jsr305.version>3.0.2</jsr305.version>
        <!-- 打包配置 -->
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <dependencies>
        <!-- ================================ -->
        <!-- 核心Spring Boot依赖 -->
        <!-- ================================ -->
        <!-- Spring Boot Web Starter - 提供Web应用基础功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Boot Actuator - 提供应用监控和管理功能 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Spring Retry - 提供重试机制 -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <!-- ================================ -->
        <!-- Spring Cloud微服务依赖 -->
        <!-- ================================ -->
        <!-- Spring Cloud Commons - 微服务公共组件 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>

        <!-- Nacos服务发现 - 提供服务注册与发现功能 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- ================================ -->
        <!-- 日志系统依赖 -->
        <!-- ================================ -->
        <!-- Log4j2核心依赖 - 高性能日志框架 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Log4j2 JSON模板布局 - 支持结构化日志输出 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
            <version>${log4j2.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Log4j2桥接器 - 统一日志框架 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
            <version>${log4j2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jul</artifactId>
            <version>${log4j2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Disruptor - Log4j2异步日志性能优化 -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>${disruptor.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 链路追踪依赖 -->
        <!-- ================================ -->
        <!-- SkyWalking链路追踪 - 分布式系统性能监控 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
            <version>${skywalking.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>${skywalking.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 监控指标依赖 -->
        <!-- ================================ -->
        <!-- Micrometer核心 - 应用指标收集 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <version>${micrometer.version}</version>
        </dependency>

        <!-- Prometheus监控 - 指标暴露和收集 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>${micrometer.version}</version>
        </dependency>

        <!-- Arthas诊断工具 - 在线Java应用诊断 -->
        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <version>${arthas.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 开发工具依赖 -->
        <!-- ================================ -->
        <!-- Lombok - 减少样板代码 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- Apache Commons Lang3 - 通用工具类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Hutool工具类 - 国产Java工具包 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- Janino编译器 - 运行时Java代码编译 -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>${janino.version}</version>
        </dependency>

        <!-- JSR305注解 - 代码质量检查 -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>${jsr305.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- JSON处理依赖 -->
        <!-- ================================ -->
        <!-- todo: 是否切换FastJSON2 - 高性能JSON处理库(替换FastJSON) -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <!-- ================================ -->
        <!-- 云存储依赖 -->
        <!-- ================================ -->
        <!-- 七牛云存储SDK -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <!-- 阿里云OSS存储SDK -->
        <!--        <dependency>-->
        <!--            <groupId>com.aliyun.oss</groupId>-->
        <!--            <artifactId>aliyun-sdk-oss</artifactId>-->
        <!--            <version>${aliyun-oss.version}</version>-->
        <!--        </dependency>-->

        <!-- ================================ -->
        <!-- Excel处理依赖 -->
        <!-- ================================ -->
        <!-- EasyExcel - 替换EasyPOI，性能更好且支持大文件 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 数据库相关依赖 -->
        <!-- ================================ -->
        <!-- Redis Starter - Redis缓存支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Jedis客户端 - Redis Java客户端 -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
            <optional>true</optional>
        </dependency>

        <!-- Druid连接池 - 数据库连接池 -->
        <!-- Druid连接池 - 支持Spring Boot 3.x -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 动态数据源 - 多数据源支持 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>

        <!-- ================================ -->
        <!-- 安全相关依赖 -->
        <!-- ================================ -->
        <!-- BouncyCastle加密库 - 国密算法支持 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 配置中心依赖 -->
        <!-- ================================ -->
        <!-- Apollo配置中心客户端 -->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo.version}</version>
        </dependency>

        <!-- ================================ -->
        <!-- 自定义业务依赖 -->
        <!-- ================================ -->
        <!-- 自定义公共语言包 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang2</artifactId>
        </dependency>

        <!-- ==================== API文档和接口 ==================== -->
        <!-- Swagger注解 - 支持Jakarta EE -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations-jakarta</artifactId>
        </dependency>

        <!-- Knife4j API文档 - 支持Spring Boot 3.x -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>

        <!-- 自定义告警组件 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-alarm2</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logstash-logback-encoder</artifactId>
                    <groupId>net.logstash.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- todo：自定义Sentinel限流组件 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-sentinel-spring-boot-starter2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- todo:灰度发布组件没有升级 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.hydee.starter</groupId>-->
        <!--            <artifactId>grey-spring-boot-web-starter</artifactId>-->
        <!--            <version>2.0.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!-- todo: 没有升级 这个影响DruidDataSourceMetricsBinderRegister Seata分布式事务 & ShardingSphere分库分表 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.hydee.starter</groupId>-->
        <!--            <artifactId>seata-shardingsphere-spring-boot-starter</artifactId>-->
        <!--            <version>1.0.0-SNAPSHOT</version>-->
        <!--            <optional>true</optional>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>com.alibaba</groupId>-->
        <!--                    <artifactId>fastjson</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

    </dependencies>

    <repositories>
        <repository>
            <id>central</id>
            <name>central</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>

        <repository>
            <id>hydee</id>
            <name>hydee</name>
            <url>http://nexus.hxyxt.com/nexus/content/groups/public</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.9.10</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>