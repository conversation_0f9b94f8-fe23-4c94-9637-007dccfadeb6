package com.yxt.starter.filter.assist.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 登录态过滤器 Properties
 *
 * <AUTHOR>
 * @date 2023/10/17 20:14
 **/
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "operate-context-filter")
public class OperateContextFilterProperties {

    /**
     * 要过滤的url
     */
    private String[] urlPatterns = new String[]{"/*"};
}
