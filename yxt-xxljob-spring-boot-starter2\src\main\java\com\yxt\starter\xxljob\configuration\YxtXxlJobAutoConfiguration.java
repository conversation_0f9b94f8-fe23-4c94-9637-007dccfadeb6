package com.yxt.starter.xxljob.configuration;

import com.xxl.job.core.util.IpUtil;
import com.yxt.starter.xxljob.executor.XxlYxtCustomExecutor;
import com.yxt.starter.xxljob.properties.XxlExecutorProperties;
import com.yxt.starter.xxljob.properties.XxlJobProperties;
import com.yxt.starter.xxljob.register.XXLShutdownAspect;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-05
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(XxlJobProperties.class)
public class YxtXxlJobAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(YxtXxlJobAutoConfiguration.class);

    public YxtXxlJobAutoConfiguration() {
      log.info("YxtXxlJobAutoConfiguration 初始化");
    }

    /**
     * 默认服务名
     */
    @Value("${spring.application.name}")
    private String applicationName;


    @Bean
    public XXLShutdownAspect xxlShutdownAspect(XxlYxtCustomExecutor xxlYxtCustomExecutor){
        return new XXLShutdownAspect(xxlYxtCustomExecutor);
    }

    /*@Bean
    public XxlJobSpringExecutor xxlJobExecutor(XxlJobProperties jobProperties) {
        log.info(">>>>>>>>>>> xxl-job config init.");

        XxlExecutorProperties executor = jobProperties.getExecutor();

        String appName = ObjectUtils.isEmpty(executor.getAppName()) ? this.applicationName : executor.getAppName();
        String ipAddress = ObjectUtils.isEmpty(executor.getIp()) ? IpUtil.getIp() : executor.getIp();
        //int port = ObjectUtils.isEmpty(executor.getPort()) || executor.getPort() <= 0 ? this.serverPort + AUTO_PORT : executor.getPort();
        String addresses = jobProperties.getAdmin().getAddresses();

        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(addresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setIp(ipAddress);
        xxlJobSpringExecutor.setPort(executor.getPort());
        xxlJobSpringExecutor.setLogPath(executor.getLogPath());
        xxlJobSpringExecutor.setLogRetentionDays(executor.getLogRetentionDays());

        log.info(">>>>>>>>>>> xxl-job config，ip：{}，port：{}，adminAddresses：{}", ipAddress, executor.getPort(), addresses);
        return xxlJobSpringExecutor;
    }*/


    @Bean
    public XxlYxtCustomExecutor xxlJobExecutor(XxlJobProperties jobProperties) {
        XxlExecutorProperties executor = jobProperties.getExecutor();

        String appName = ObjectUtils.isEmpty(executor.getAppName()) ? this.applicationName : executor.getAppName();
        String ipAddress = ObjectUtils.isEmpty(executor.getIp()) ? IpUtil.getIp() : executor.getIp();
        //int port = ObjectUtils.isEmpty(executor.getPort()) || executor.getPort() <= 0 ? this.serverPort + AUTO_PORT : executor.getPort();
        String addresses = jobProperties.getAdmin().getAddresses();

        XxlYxtCustomExecutor XxlYxtCustomExecutor = new XxlYxtCustomExecutor();
        XxlYxtCustomExecutor.setAdminAddresses(addresses);
        XxlYxtCustomExecutor.setAppname(appName);
        XxlYxtCustomExecutor.setIp(ipAddress);
        XxlYxtCustomExecutor.setPort(executor.getPort());
        XxlYxtCustomExecutor.setLogPath(executor.getLogPath());
        XxlYxtCustomExecutor.setAccessToken(jobProperties.getAccessToken());
        XxlYxtCustomExecutor.setLogRetentionDays(executor.getLogRetentionDays());
        return XxlYxtCustomExecutor;
    }

}
