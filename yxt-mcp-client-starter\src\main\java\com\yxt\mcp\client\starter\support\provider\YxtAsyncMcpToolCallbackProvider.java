package com.yxt.mcp.client.starter.support.provider;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.mcp.client.starter.support.tool.callback.YxtAsyncMcpToolCallback;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.spec.McpClientSession;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.InitializeResult;
import io.modelcontextprotocol.spec.McpSchema.ListToolsResult;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.mcp.AsyncMcpToolCallbackProvider;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.support.ToolUtils;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

/**
 * <AUTHOR>
 * @Description 扩展的 MCP 工具回调提供者，支持用户权限信息透传
 * @Date 2025/06/11/13:31
 */
public class YxtAsyncMcpToolCallbackProvider extends AsyncMcpToolCallbackProvider {

    private static final Logger logger = LoggerFactory.getLogger(YxtAsyncMcpToolCallbackProvider.class);
    /**
     * 用于初始化完成信号的 Sink
     */
    protected final Sinks.One<McpSchema.InitializeResult> initializedSink = Sinks.one();

    /**
     * 用于保存 MCP 客户端列表
     */
    private final List<McpAsyncClient> _mcpClients;

    /**
     * 用于过滤工具
     */
    private final BiPredicate<McpAsyncClient, Tool> _toolFilter;

    /**
     * 用于保存列表工具结果类型的 TypeReference
     */
    private static final TypeReference<ListToolsResult> LIST_TOOLS_RESULT_TYPE_REF = new TypeReference<>() {
    };

    /**
     * 构造函数带过滤器
     *
     * @param toolFilter
     * @param mcpClients
     */
    public YxtAsyncMcpToolCallbackProvider(BiPredicate<McpAsyncClient, Tool> toolFilter,
        List<McpAsyncClient> mcpClients) {
        super(toolFilter, mcpClients);
        this._mcpClients = mcpClients;
        this._toolFilter = toolFilter;
    }

    /**
     * 构造函数不过滤任何工具。
     *
     * @param mcpClients
     */
    public YxtAsyncMcpToolCallbackProvider(List<McpAsyncClient> mcpClients) {
        this((mcpClient, tool) -> true, mcpClients);
    }

    //重新，使用YXT的 用于身份信息透传
    @Override
    public ToolCallback[] getToolCallbacks() {

        List<ToolCallback> toolCallbackList = new ArrayList<>();
        //遍历每个 MCP 客户端
        for (McpAsyncClient mcpClient : this._mcpClients) {
            //调用 listTools() 获取工具；
            ToolCallback[] toolCallbacks = mcpClient.listTools()
                .map(response -> response.tools()
                    .stream()
                    .filter(tool -> this._toolFilter.test(mcpClient, tool))
                    .map(tool -> new YxtAsyncMcpToolCallback(mcpClient, tool))
                    .toArray(ToolCallback[]::new))
                .block();

            validateToolCallbacks(toolCallbacks);

            toolCallbackList.addAll(List.of(toolCallbacks));
        }
        return toolCallbackList.toArray(new ToolCallback[0]);
    }

    /**
     * 扩展透传用户权限信息
     *
     * @param params
     * @return
     */
    public ToolCallback[] getAuthToolCallbacks(Object params) {
        List<ToolCallback> toolCallbackList = new ArrayList<>();

        for (McpAsyncClient mcpClient : this._mcpClients) {
            Mono<ListToolsResult> listToolsResultMono = null;
            try {
                //liqiangtodo 增强 list/tool 增加用户信息透传
                listToolsResultMono = sendRequest(mcpClient,
                    McpSchema.METHOD_TOOLS_LIST,
                    params,
                    LIST_TOOLS_RESULT_TYPE_REF)
                    .onErrorResume(e -> {
                        logger.error("Error occurred while sending request", e);
                        return Mono.empty(); // 或者根据需求返回默认值
                    });

                ToolCallback[] toolCallbacks = listToolsResultMono
                    .map(response -> response.tools()
                        .stream()
                        .filter(tool -> this._toolFilter.test(mcpClient, tool))
                        .map(tool -> new YxtAsyncMcpToolCallback(mcpClient, tool))
                        .toArray(ToolCallback[]::new))
                    .block(); // 注意：这里仍然使用了 block()，建议改为非阻塞方式

                if (toolCallbacks != null && toolCallbacks.length > 0) {
                    validateToolCallbacks(toolCallbacks);
                    toolCallbackList.addAll(List.of(toolCallbacks));
                }
            } catch (Exception e) {
                logger.error("Failed to get tool callbacks for client: {}", mcpClient, e);
            }
        }

        return toolCallbackList.toArray(new ToolCallback[0]);
    }

    /**
     * 检查是否存在同名工具，避免冲突
     * @param toolCallbacks 工具回调
     */
    private void validateToolCallbacks(ToolCallback[] toolCallbacks) {
        List<String> duplicateToolNames = ToolUtils.getDuplicateToolNames(toolCallbacks);
        if (!duplicateToolNames.isEmpty()) {
            throw new IllegalStateException(
                "Multiple tools with the same name (%s)".formatted(String.join(", ", duplicateToolNames)));
        }
    }

    /**
     * 利用反射调用 McpClientSession.sendRequest(...) 方法发送请求
     */
    public <T> Mono<T> sendRequest(McpAsyncClient mcpClient, String method, Object requestParams,
        TypeReference<T> typeRef) {

        return this.withInitializationCheck("listing tools", initializedResult -> {
            // 获取 mcpSession 字段
            Field mcpSessionField;
            try {
                mcpSessionField = mcpClient.getClass().getDeclaredField("mcpSession");
            } catch (NoSuchFieldException e) {
                throw new RuntimeException(e);
            }
            mcpSessionField.setAccessible(true);

            // 获取 mcpSession 实例
            McpClientSession mcpSession = null;
            try {
                mcpSession = (McpClientSession) mcpSessionField.get(mcpClient);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }

            // 获取 sendRequest 方法
            Method sendRequestMethod = null;
            try {
                sendRequestMethod = mcpSession.getClass()
                    .getMethod("sendRequest", String.class, Object.class, TypeReference.class);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }

            // 调用 sendRequest 并获取 Mono 结果
            @SuppressWarnings("unchecked")
            Mono<T> result = null;
            try {
                result = (Mono<T>) sendRequestMethod.invoke(mcpSession, method, requestParams, typeRef);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (InvocationTargetException e) {
                throw new RuntimeException(e);
            }
            return result;

        });
    }

    private <T> Mono<T> withInitializationCheck(String actionName,
        Function<InitializeResult, Mono<T>> operation) {
        return operation.apply(null); // 直接执行操作，不检查初始化
    }
}
