package com.yxt.starter.configuration.condition;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Arrays;
import java.util.List;

/**
 * @desc linux环境判断
 *
 * <AUTHOR>
 * @date 2024/5/23 17:29
 */
public class LinuxEnvironmentCondition implements Condition {

    /**
     * 本地、开发、测试环境
     */
    private final List<String> FORBID_ENV = Arrays.asList("local", "dev", "test");

    private final String WINDOWS = "windows";

    private final String MAC = "mac";

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        if ("true".equals(context.getEnvironment().getProperty("middleware.register.rocketMQSwitch"))) {
            return true;
        }
        // 环境
        String env = context.getEnvironment().getProperty("spring.profiles.active");
        // 获取操作系统名称
        String osName = System.getProperty("os.name").toLowerCase();
        // 本地、开发、测试环境，且是windows或是mac操作系统则不注册到spring容器中
        if (isForbidEnv(env) && (isWindows(osName) || isMac(osName))) {
            return false;
        }
        return true;
    }

    private boolean isWindows(String osName) {
        return StringUtils.isNotBlank(osName) && osName.contains(WINDOWS);
    }

    private boolean isMac(String osName) {
        return StringUtils.isNotBlank(osName) && osName.contains(MAC);
    }

    private boolean isForbidEnv(String env) {
        String lowerCaseEnv = org.apache.commons.lang3.StringUtils.lowerCase(env);
        for (String forbidEnv : FORBID_ENV) {
            if (lowerCaseEnv.contains(forbidEnv)) {
                return true;
            }
        }
        return false;
    }

}
