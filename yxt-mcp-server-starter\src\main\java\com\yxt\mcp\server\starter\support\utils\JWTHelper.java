package com.yxt.mcp.server.starter.support.utils;


import com.yxt.mcp.server.starter.support.datasource.dto.JWTInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;


/**
 * jwt帮助类
 */
public class JWTHelper {

    private static RsaKeyHelper rsaKeyHelper = new RsaKeyHelper();
    private static final String JWT_KEY_NAME = "name";
    private static final String JWT_KEY_ZH_NAME = "zh-name";
    private static final String JWT_KEY_MERCODE = "mer-code";
    /**
     * 员工编码标识
     */
    private static final String JWT_KEY_EMP_CODE = "emp-code";
    /**
     * 账号类型标识
     */
    private static final String JWT_KEY_ACC_TYPE = "acc-type";
    /**
     * 多端登录标识
     */
    private static final String JWT_KEY_MULTI_LOGIN = "multi-login";

    /**
     * 获取token中的用户信息
     *
     * @param token
     * @param pubKey
     * @return
     * @throws Exception
     */
    public static JWTInfo getInfoFromToken(String token, byte[] pubKey) throws Exception {
        Jws<Claims> claimsJws = parserToken(token, pubKey);
        Claims body = claimsJws.getBody();
        Object accTypeObj = body.get(JWT_KEY_ACC_TYPE);
        return new JWTInfo(body.getSubject(), StringHelper.getObjectValue(body.get(JWT_KEY_NAME))
            , StringHelper.getObjectValue(body.get(JWT_KEY_ZH_NAME)),
            StringHelper.getObjectValue(body.get(JWT_KEY_MERCODE)),
            StringHelper.getObjectValue(body.get(JWT_KEY_EMP_CODE)),
            accTypeObj == null ? null : Integer.valueOf(accTypeObj.toString()),
            StringHelper.getObjectValue(body.get(JWT_KEY_MULTI_LOGIN)));
    }

    /**
     * 公钥解析token
     *
     * @param token
     * @return
     * @throws Exception
     */
    public static Jws<Claims> parserToken(String token, byte[] pubKey) throws Exception {
        Jws<Claims> claimsJws = Jwts.parser().setSigningKey(rsaKeyHelper.getPublicKey(pubKey)).parseClaimsJws(token);
        return claimsJws;
    }
}
