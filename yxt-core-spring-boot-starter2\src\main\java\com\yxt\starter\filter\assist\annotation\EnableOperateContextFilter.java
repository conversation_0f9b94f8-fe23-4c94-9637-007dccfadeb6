package com.yxt.starter.filter.assist.annotation;

import com.yxt.starter.filter.assist.config.OperateContextFilterConfig;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.context.annotation.Import;

/**
 * 启动登录态过滤器的注解
 *
 * <AUTHOR>
 * @date 2023/10/17 19:53
 **/
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({OperateContextFilterConfig.class})
@Documented
public @interface EnableOperateContextFilter {
}
