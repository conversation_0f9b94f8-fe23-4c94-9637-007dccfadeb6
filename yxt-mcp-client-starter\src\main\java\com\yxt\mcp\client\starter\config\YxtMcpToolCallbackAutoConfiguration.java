package com.yxt.mcp.client.starter.config;

import com.yxt.mcp.client.starter.support.provider.YxtAsyncMcpToolCallbackProvider;
import com.yxt.mcp.client.starter.support.tool.resolver.YxtToolCallbackResolver;
import io.modelcontextprotocol.client.McpAsyncClient;
import java.util.List;
import org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @Description 增加tool call调用
 * @Date 2025/06/11/13:35
 */
@AutoConfiguration
@AutoConfigureBefore(McpToolCallbackAutoConfiguration.class)
@ComponentScan(basePackages = "com.yxt.mcp.client.starter.entrance.observation")
public class YxtMcpToolCallbackAutoConfiguration {


    //liqiangtodo 增强tool call 增加传递用户身份信息
    @Bean
    public YxtAsyncMcpToolCallbackProvider yxtAsyncMcpToolCallbackProvider(
        ObjectProvider<List<McpAsyncClient>> mcpClientsProvider) {
        List<McpAsyncClient> mcpClients = mcpClientsProvider.stream().flatMap(List::stream).toList();
        return new YxtAsyncMcpToolCallbackProvider(mcpClients);
    }


    /**
     * org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration#toolCallbackResolver 覆盖
     * 避免拉全部tool，client是根据用户授权来做控制
     *
     * @return
     */
    @Bean
    public YxtToolCallbackResolver yxtToolCallbackResolver() {
        return new YxtToolCallbackResolver();
    }

}
