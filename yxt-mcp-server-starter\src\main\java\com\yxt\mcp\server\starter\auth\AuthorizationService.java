package com.yxt.mcp.server.starter.auth;

import io.modelcontextprotocol.spec.McpSchema.Tool;
import java.util.List;
import java.util.Optional;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Description 授权接口
 * @Date 2025/06/13/14:58
 */
public interface AuthorizationService {

    /**
     * 解析调用身份信息
     *
     * @param params
     * @param yxtUserId
     * @param businessScenario
     * @return
     */
    YxtAuthResult parseInvokeAuthResult(String appId, String yxtUserId, String yxtEmpCode,
        String businessScenario, Object params);

    /**
     * 解析用户ID 支持2中一种是 dify直接透传 一种是idea 传递tonken
     *
     * @param token 用户身份验证令牌
     * @return 返回解析出的用户ID字符串
     */
    Optional<YxtAuthResult> parseYxtAuthResult(String appId, String businessScenario, String userId, String empCode,
        String token);

    /**
     * 校验SSE权限
     *
     * @param appId
     * @param businessScenario
     * @param yxtAuthResult
     * @return
     */
    boolean checkSSEPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult);

    /**
     * 过滤Tool权限
     *
     * @param yxtAuthResult
     * @param appId
     * @param businessScenario
     * @param toolList
     * @return
     */
    List<Tool> listToolsByPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult,
        List<Tool> toolList);


    /**
     * 调用工具权限校验
     *
     * @param yxtAuthResult
     * @param appId
     * @param toolName
     * @return
     */
    Mono<Boolean> checkCallToolPermission(String appId, String businessScenario, YxtAuthResult yxtAuthResult,
        String toolName);

    record YxtAuthResult(String yxtUserId, String yxtEmpCode, String businessScenario, String operatorLogin,
                         String operatorName,
                         String operatorSource) {

    }
}
