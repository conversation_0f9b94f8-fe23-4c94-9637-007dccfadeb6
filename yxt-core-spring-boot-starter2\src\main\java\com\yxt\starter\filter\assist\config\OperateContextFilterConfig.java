package com.yxt.starter.filter.assist.config;

import com.yxt.starter.filter.assist.OperateContextFilter;
import jakarta.annotation.Resource;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;


/**
 * 登录态过滤器配置
 *
 * <AUTHOR>
 * @date 2023年10月16日 13:35
 */
public class OperateContextFilterConfig {

    @Resource
    private OperateContextFilterProperties operateContextFilterProperties;

    @Resource
    private OperateContextFilter operateContextFilter;

    @Bean
    public OperateContextFilter operateContextFilter() {
        return new OperateContextFilter();
    }

    @Bean
    public FilterRegistrationBean<OperateContextFilter> registrationProjectFilter() {
        FilterRegistrationBean<OperateContextFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(operateContextFilter);
        registration.addUrlPatterns(operateContextFilterProperties.getUrlPatterns());
        return registration;
    }
}
