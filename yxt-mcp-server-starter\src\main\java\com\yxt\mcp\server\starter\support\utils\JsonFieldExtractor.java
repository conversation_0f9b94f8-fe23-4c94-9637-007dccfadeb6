package com.yxt.mcp.server.starter.support.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.Arrays;
import java.util.List;


public class JsonFieldExtractor {

    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * 根据传入的字段路径列表，从原始 JSON 中提取保留父级结构的子对象
     *
     * @param jsonStr           原始 JSON 字符串
     * @param includeFieldPaths 需要提取的字段路径数组（支持多个）
     * @return 提取后的 JSON 子结构
     */
    public static String extractFields(String jsonStr, List<String> includeFieldPaths) throws Exception {
        if (!isJsonValid(jsonStr)) {
            return jsonStr;
        }
        JsonNode root = mapper.readTree(jsonStr);
        ObjectNode result = mapper.createObjectNode();

        for (String path : includeFieldPaths) {
            String[] segments = path.split("\\.");
            buildSubStructure(root, result, segments, 0);
        }

        return result.toPrettyString();
    }

    /**
     * 递归构建目标结构
     */
    private static boolean buildSubStructure(JsonNode source, ObjectNode target, String[] pathSegments, int depth) {
        if (depth >= pathSegments.length || !source.has(pathSegments[depth])) {
            return false;
        }

        String currentKey = pathSegments[depth];
        JsonNode currentValue = source.get(currentKey);

        if (depth == pathSegments.length - 1) {
            ((ObjectNode) target).set(currentKey, currentValue);
            return true;
        } else {
            ObjectNode nextLevel = mapper.createObjectNode();
            boolean success = buildSubStructure(currentValue, nextLevel, pathSegments, depth + 1);
            if (success) {
                ((ObjectNode) target).set(currentKey, nextLevel);
                return true;
            }
        }
        return false;
    }

    public static boolean isJsonValid(String jsonString) {
        if (jsonString == null || jsonString.isBlank()) {
            return false;
        }
        try {
            mapper.readTree(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) throws Exception {
        String jsonStr = "{\n" +
            "    \"yxtToolProvider\": {\n" +
            "        \"protocol\": \"confluence\",\n" +
            "        \"protocolMeta\": {\n" +
            "            \"url\": \"${url}\",\n" +
            "            \"excelue\":[\"user.info\", \"\"],\n" +
            "            \"include\":[\"yyxt.info\", \"\"]\n" +
            "        },\n" +
            "        \"alarmTagIdList\": [11]\n" +
            "    },\n" +
            "    \"properties\": {\n" +
            "        \"url\": {\"type\": \"string\", \"description\": \"规范地址url\"}\n" +
            "    }\n" +
            "}";

        List<String> includeList = Arrays.asList("yxtToolProvider.protocolMeta.url", "properties.url");

        String result = JsonFieldExtractor.extractFields(jsonStr, includeList);
        System.out.println(result);
    }
}

