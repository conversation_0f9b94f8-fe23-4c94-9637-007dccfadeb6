package com.yxt.dynamic.routing.agent.core;

import javassist.CannotCompileException;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtMethod;
import javassist.Modifier;
import javassist.NotFoundException;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/5/21 18:01
 */
public class JMethod {
    private JClass jClass;
    private String methodName;
    private String[] paramsClass;
    private List<MethodAction> methodActions = new ArrayList<>();
    private boolean removed;

    private List<JMethodAnnotation> jAnnotationList = new ArrayList<>();

    public JMethod(String methodName, JClass jClass,String...paramsClass) {
        this.jClass = jClass;
        this.methodName = methodName;
        this.paramsClass = paramsClass;
    }

    public JClass then() {
        return this.jClass;
    }

    protected CtClass apply(CtClass ctClass) {
        try {
            for (MethodAction methodAction : methodActions) {
                ctClass = methodAction.action(ctClass);
            }
        } catch (NotFoundException e) {
            System.out.println("javasisit error:" + this.methodName+" " + e.getMessage());
        } catch (CannotCompileException e) {
            System.out.println("javasisit error:" + this.methodName+" " +  e.getMessage());
        } catch (Exception e) {
            System.out.println("javasisit error:" + this.methodName+" " +  e.getMessage());
        }
        return ctClass;
    }

    public JMethod remove() {
        methodActions.add(new RemoveAction(this.methodName,createCtClasses(paramsClass)));
        removed = true;
        return this;
    }

    public JMethod changeMethod(String methodContent) {
        methodActions.add(new ChangeAction(this.methodName, methodContent,createCtClasses(paramsClass)));
        return this;
    }

    public JMethod overrideMethod(String methodContent) {
        methodActions.add(new OverrideAction(this.methodName, methodContent,createCtClasses(paramsClass)));
        return this;
    }

    public JMethod insertBefore(String methodContent) {
        methodActions.add(new InsertBefore(this.methodName, methodContent,createCtClasses(paramsClass)));
        return this;
    }

    public JMethod insertAllBefore(String methodContent) {
        methodActions.add(new InsertAllBefore(this.methodName, methodContent));
        return this;
    }

    public JMethod insertAfter(String methodContent,boolean asFinally) {
        methodActions.add(new InsertAfter(this.methodName, methodContent,asFinally,createCtClasses(paramsClass)));
        return this;
    }

    boolean isRemoved() {
        return removed;
    }

    public JMethodAnnotation jAnnotation(String annName) {
        JMethodAnnotation jMethodAnnotation = new JMethodAnnotation(this, annName);
        jAnnotationList.add(jMethodAnnotation);
        return jMethodAnnotation;
    }

    private CtClass[] createCtClasses(String[] paramClazz) {
        if(paramClazz==null || paramClazz.length==0){
            return null;
        }
        CtClass[] ctClasses = new CtClass[paramClazz.length];
        // 创建ClassPool实例，它是管理CtClass的容器
        ClassPool pool = ClassPool.getDefault();
        // 定义一个新的CtClass，这将是我们要创建的类
        for (int i = 0; i < paramClazz.length; i++) {
            ctClasses[i] = pool.makeClass(paramClazz[i]);
        }
        return ctClasses;
    }

    private static class RemoveAction implements MethodAction {
        public RemoveAction(String methodName, CtClass[] paramsClass) {
            this.methodName = methodName;
            this.paramsClass = paramsClass;
        }

        private String methodName;
        private CtClass[] paramsClass;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtMethod declaredMethod;
            if(paramsClass==null){
                declaredMethod = ctClass.getDeclaredMethod(this.methodName);
            }else {
                declaredMethod = ctClass.getDeclaredMethod(this.methodName,paramsClass);
            }
            ctClass.removeMethod(declaredMethod);
            return ctClass;
        }
    }

    private static class ChangeAction implements MethodAction {
        public ChangeAction(String methodName, String content,CtClass ... paramClazz) {
            this.methodName = methodName;
            this.content = content;
            this.paramClazz = paramClazz;
        }

        private String methodName;
        private String content;
        private CtClass[] paramClazz;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtMethod declaredMethod;
            if(paramClazz==null){
                declaredMethod = ctClass.getDeclaredMethod(this.methodName);
            }else {
                declaredMethod = ctClass.getDeclaredMethod(this.methodName, paramClazz);
            }
            declaredMethod.setBody(this.content);
            return ctClass;
        }

    }

    private static class InsertBefore implements MethodAction {
        public InsertBefore(String methodName, String content,CtClass ... paramClazz) {
            this.methodName = methodName;
            this.content = content;
            this.paramClazz = paramClazz;
        }

        private String methodName;
        private String content;
        private CtClass[] paramClazz;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtMethod declaredMethod;
            if(paramClazz==null){
                declaredMethod = ctClass.getDeclaredMethod(this.methodName);
            }else {
                declaredMethod = ctClass.getDeclaredMethod(this.methodName, paramClazz);
            }
            declaredMethod.insertBefore(this.content);
            return ctClass;
        }

    }

    private static class InsertAllBefore implements MethodAction {
        public InsertAllBefore(String methodName, String content) {
            this.methodName = methodName;
            this.content = content;
        }

        private String methodName;
        private String content;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtMethod[] declaredMethods = ctClass.getDeclaredMethods(this.methodName);
            for (CtMethod declaredMethod : declaredMethods) {
                declaredMethod.insertBefore(this.content);
            }
            return ctClass;
        }

    }

    private static class InsertAfter implements MethodAction {
        public InsertAfter(String methodName, String content, boolean asFinally, CtClass ... paramClazz) {
            this.methodName = methodName;
            this.content = content;
            this.paramClazz = paramClazz;
            this.asFinally = asFinally;
        }

        private String methodName;
        private String content;
        private boolean asFinally;
        private CtClass[] paramClazz;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtMethod declaredMethod;
            if(paramClazz==null){
                declaredMethod = ctClass.getDeclaredMethod(this.methodName);
            }else {
                declaredMethod = ctClass.getDeclaredMethod(this.methodName, paramClazz);
            }
            declaredMethod.insertAfter(this.content,asFinally);
            return ctClass;
        }

    }

    /**
     * 覆盖父类函数
     */
    private static class OverrideAction implements MethodAction {
        public OverrideAction(String methodName, String content, CtClass[] paramsClass) {
            this.methodName = methodName;
            this.content = content;
            this.paramsClass = paramsClass;
        }

        private String methodName;
        private String content;
        private CtClass[] paramsClass;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            // 创建新方法
            CtMethod newMethod;
            if(paramsClass==null){
                newMethod = new CtMethod(CtClass.voidType, methodName, new CtClass[]{}, ctClass); // 方法名、返回类型、参数类型列表、所属类
            }else {
                newMethod = new CtMethod(CtClass.voidType, methodName, paramsClass, ctClass); // 方法名、返回类型、参数类型列表、所属类
            }

            newMethod.setModifiers(Modifier.PUBLIC); // 设置访问修饰符为public
            newMethod.setBody(content); // 设置方法体

            // 添加@Override注解
            ConstPool constPool = ctClass.getClassFile().getConstPool();
            AnnotationsAttribute attr = new AnnotationsAttribute(constPool, AnnotationsAttribute.visibleTag);
            Annotation overrideAnno = new Annotation("Ljava/lang/Override", constPool);
            attr.addAnnotation(overrideAnno);
            newMethod.getMethodInfo().addAttribute(attr);

            ctClass.addMethod(newMethod);
            return ctClass;
        }
    }




    interface MethodAction {
        CtClass action(CtClass ctClass) throws Exception;
    }

}
