package com.yxt.starter.log4j2;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.Node;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;
import org.apache.logging.log4j.core.filter.AbstractFilter;
import org.apache.logging.log4j.core.impl.ThrowableProxy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yxt.common.alarm.util.AlarmConst.ALARM_KEYWORDS;

/**
 * log4j2  自定义Filter添加堆栈信息
 */
@Slf4j
//声明插件的类型，名字等信息
@Plugin(name = "StackTraceFilter", category = Node.CATEGORY, elementType = Filter.ELEMENT_TYPE, printObject = true)
public class StackTraceFilter extends AbstractFilter {

    private final String threadName;
    private final Result onMatch;
    private final Result onMisMatch;
    private final Level level;
    private final boolean isConsole;

    private static Set<String> keywordsset;

    static {
        keywordsset = Arrays.stream(ALARM_KEYWORDS).collect(Collectors.toSet());
    }

    public StackTraceFilter(String threadName, Result onMatch, Result onMisMatch, Level level, boolean isConsole) {
        super();
        this.threadName = threadName;
        this.onMatch = onMatch;
        this.onMisMatch = onMisMatch;
        this.level = level;
        this.isConsole = isConsole;
    }

    /**
     * Create a ThreadFilter instance.
     *
     * @param threadName The thread name
     * @param match      The action to take on a match.
     * @param mismatch   The action to take on a mismatch.
     * @param level      log level
     * @return The created MyCustomFilter.
     */
    //@PluginFactory注解对应的必须使用一个静态的方法，传入的参数用@PluginAttribute修饰
    @PluginFactory
    public static StackTraceFilter createFilter(@PluginAttribute("threadName") final String threadName,
                                                @PluginAttribute("onMatch") final Result match,
                                                @PluginAttribute("onMismatch") final Result mismatch,
                                                @PluginAttribute("isConsole") final boolean isConsole,
                                                @PluginAttribute("level") final Level level) {
        assert StringUtils.isNotBlank(threadName);
        final Result onMatch = match == null ? Result.NEUTRAL : match;
        final Result onMismatch = mismatch == null ? Result.DENY : mismatch;
        final Level levelResult = level == null ? Level.ERROR : level;
        return new StackTraceFilter(threadName, onMatch, onMismatch, levelResult, isConsole);
    }

    @Override
    public Result filter(LogEvent event) {
        try {
            if (event.getLevel().intLevel() > this.level.intLevel() || !(event instanceof YxtLog4jLogEvent)) {
                if (isConsole) {
                    return Result.NEUTRAL;
                }
                return this.onMisMatch;
            }
            ThrowableProxy proxy = event.getThrownProxy();

            //默认 error 级别的日志 没有堆栈信息  手动追加上堆栈信息
            if ((proxy == null || proxy.getStackTrace() == null)) {
                YxtLog4jLogEvent yxtLog4jLogEvent = (YxtLog4jLogEvent) event;
                StackTraceElement[] stackTrace = yxtLog4jLogEvent.getStackTrace();
                if (stackTrace == null) {
                    if (isConsole) {
                        return Result.NEUTRAL;
                    }
                    return this.onMisMatch;
                }

                //获取堆栈信息
                ArrayList<StackTraceElement> stackTraceList = new ArrayList<>();
                int stackLen = stackTrace.length;
                int index = stackLen > 3 ? 3 : 0;
                for (int i = index; i < stackLen; i++) {
                    String stackTraceStr = stackTrace[i].toString();
                    if (i > stackLen - 4) {
                        stackTraceList.add(stackTrace[i]);
                        continue;
                    }
                    if (hitAlarmKeywords(stackTraceStr)) {
                        stackTraceList.add(stackTrace[i]);
                    }
                }
                Throwable throwable = new Throwable("");
                StackTraceElement[] array = stackTraceList.toArray((new StackTraceElement[0]));
                throwable.setStackTrace(array);
                yxtLog4jLogEvent.setThrown(throwable);
            }

            if (isConsole) {
                return Result.NEUTRAL;
            }
            if (event.getLevel().intLevel() <= this.level.intLevel()) {
                return this.onMatch;
            }
        } catch (Exception e) {
            log.warn("StackTraceFilter error", e);
        }
        return this.onMisMatch;
    }

    protected static boolean hitAlarmKeywords(String stackTraceStr) {
        if (StringUtils.isBlank(stackTraceStr)) {
            return false;
        }

        for (String keyWord : keywordsset) {
            if (stackTraceStr.contains(keyWord)) {
                return true;
            }
        }
        return false;
    }


}
