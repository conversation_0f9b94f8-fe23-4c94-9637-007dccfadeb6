package com.yxt.dynamic.routing.agent.core;

import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/9 17:33
 */
public class JConstructor {
    String[] paramsClass;
    JClass jClass;

    private List<ConstructorAction> constructorActions = new ArrayList<>();

    public JConstructor(JClass jClass) {
        this(jClass,null);
    }

    public JConstructor(JClass jClass, String[] paramsClass) {
        this.jClass = jClass;
        this.paramsClass = paramsClass;
    }

    protected CtClass apply(CtClass ctClass) throws Exception {
        for (ConstructorAction constructorAction : constructorActions) {
            ctClass = constructorAction.action(ctClass);
        }
        return ctClass;
    }

    public JClass then() {
        return this.jClass;
    }

    public JConstructor insertAfter(String content, boolean asFinally) {
        constructorActions.add(new InsertAfter(content,asFinally,createCtClasses(this.paramsClass)));
        return this;
    }
    public JConstructor insertBefore(String content) {
        constructorActions.add(new InsertBefore(content,createCtClasses(this.paramsClass)));
        return this;
    }
    private CtClass[] createCtClasses(String[] paramClazz) {
        if(paramClazz==null || paramClazz.length==0){
            return null;
        }
        CtClass[] ctClasses = new CtClass[paramClazz.length];
        // 创建ClassPool实例，它是管理CtClass的容器
        ClassPool pool = ClassPool.getDefault();
        // 定义一个新的CtClass，这将是我们要创建的类
        for (int i = 0; i < paramClazz.length; i++) {
            ctClasses[i] = pool.makeClass(paramClazz[i]);
        }
        return ctClasses;
    }


    private static class InsertBefore implements ConstructorAction {
        public InsertBefore(String content, CtClass ... paramClazz) {
            this.content = content;
            this.paramClazz = paramClazz;
        }

        private String content;
        private CtClass[] paramClazz;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtConstructor ctConstructor = ctClass.getDeclaredConstructor(this.paramClazz);
            ctConstructor.insertBefore(this.content);
            return ctClass;
        }

    }
    private static class InsertAfter implements ConstructorAction {
        public InsertAfter(String content, boolean asFinally, CtClass ... paramClazz) {
            this.content = content;
            this.paramClazz = paramClazz;
            this.asFinally = asFinally;
        }

        private String content;
        private boolean asFinally;
        private CtClass[] paramClazz;

        @Override
        public CtClass action(CtClass ctClass) throws Exception {
            CtConstructor ctConstructor = ctClass.getDeclaredConstructor(this.paramClazz);
            ctConstructor.insertAfter(this.content,asFinally);
            return ctClass;
        }

    }

    interface ConstructorAction {
        CtClass action(CtClass ctClass) throws Exception;
    }
}
