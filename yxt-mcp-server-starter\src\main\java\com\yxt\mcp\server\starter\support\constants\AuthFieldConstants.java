package com.yxt.mcp.server.starter.support.constants;

/**
 * <AUTHOR>
 * @Description 授权相关传递字段
 * @Date 2025/06/17/15:13
 */
public final class AuthFieldConstants {

    private AuthFieldConstants() {
    }
    //region 身份信息

    /**
     * appId
     */
    public static final String APP_ID = "app_id";
    /**
     * 业务场景
     */
    public static final String BUSINESS_SCENARIO = "business_scenario";

    /**
     * token
     */
    public static final String TOKEN = "token";

    /**
     * 用户类型
     */
    public static final String YXT_USER_TYPE = "yxt_user_type";

    /**
     * 操作人id (例如: 工号)
     */
    public static final String YXT_OPERATION_CONTEXT_OPERATOR_ID = "yxt_operator_id";

    /**
     * 操作人userId
     */
    public static final String YXT_OPERATION_CONTEXT_OPERATOR_USER_ID = "yxt_user_id";
    /**
     * 操作人登录名
     */
    public static final String YXT_OPERATION_CONTEXT_LOGIN = "yxt_operator_login";

    /**
     * 操作人姓名
     */
    public static final String YXT_OPERATION_CONTEXT_NAME = "yxt_operator_name";

    /**
     * 操作人类型
     */
    public static final String YXT_OPERATION_CONTEXT_SOURCE = "yxt_operator_source";
    //endregion
}
