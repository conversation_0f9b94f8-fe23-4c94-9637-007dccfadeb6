package com.yxt.starter.sentinel;

import com.yxt.starter.sentinel.constants.YxtSentinelConstants;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Author: liqiang
 * @Date: 2024/9/29 11:08
 * @Description: YxtSentinel配置
 */
@ConfigurationProperties(prefix = YxtSentinelConstants.PROPERTY_PREFIX)
@Getter
@Setter
public class YxtSentinelProperties {

    private YxtAuth auth;

    @Getter
    @Setter
    public static class YxtAuth {

        /**
         * 企业微信用户标签
         */
        private List<String> wechatTagList;

        /**
         * 用户工号
         */
        private List<String> userIdList;
    }

}
