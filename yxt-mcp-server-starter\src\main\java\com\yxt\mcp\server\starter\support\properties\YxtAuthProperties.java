package com.yxt.mcp.server.starter.support.properties;

import cn.hutool.core.codec.Base64;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @Description yxt 授权信息配置
 * @Date 2025/06/16/18:38
 */
@ConfigurationProperties(prefix = YxtAuthProperties.CONFIG_PREFIX)
public class YxtAuthProperties {

    public static final String CONFIG_PREFIX = "yxt.auth.jwt";

    private String publicKey;

    public byte[] getUserPubKey() {
        return Base64.decode(publicKey);
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }
}
