package com.yxt.mcp.server.starter.support.session;

import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import io.modelcontextprotocol.spec.McpServerSession;
import io.modelcontextprotocol.spec.McpServerTransport;
import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 一心堂自定义Session
 * @Date 2025/06/13/11:32
 */
public class YxtMcpServerSession extends McpServerSession {


    /**
     * 用户信息
     */
    private YxtAuthResult yxtAuthResult;

    /**
     * appId
     */
    private String appId;
    /**
     * 业务场景
     */
    private String businessScenario;


    public YxtMcpServerSession(String id, Duration requestTimeout,
        McpServerTransport transport, InitRequestHandler initHandler,
        InitNotificationHandler initNotificationHandler, Map<String, RequestHandler<?>> requestHandlers,
        Map<String, NotificationHandler> notificationHandlers, YxtAuthResult yxtAuthResult, String appId,
        String businessScenario) {
        super(id, requestTimeout, transport, initHandler, initNotificationHandler, requestHandlers,
            notificationHandlers);
        this.appId = appId;
        this.yxtAuthResult = yxtAuthResult;
        this.businessScenario = businessScenario;
    }

    public YxtAuthResult getYxtAuthResult() {
        return yxtAuthResult;
    }

    public void setYxtAuthResult(YxtAuthResult yxtAuthResult) {
        this.yxtAuthResult = yxtAuthResult;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getBusinessScenario() {
        return businessScenario;
    }

    public void setBusinessScenario(String businessScenario) {
        this.businessScenario = businessScenario;
    }
}
