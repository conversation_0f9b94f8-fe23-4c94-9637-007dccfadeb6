package com.yxt.mcp.server.starter.support.datasource.dto;

import com.yxt.mcp.server.starter.support.constants.AuthTargetTypeEnum;
import java.util.List;

/**
 * <AUTHOR>
 * @Description MCP授权规则
 * @Date 2025/06/16/16:36
 */
public class AuthorizationRule {

    private AuthTargetTypeEnum targetType;
    private List<?> targetList;
    private List<String> authToolList;

    public AuthTargetTypeEnum getTargetType() {
        return targetType;
    }

    public void setTargetType(AuthTargetTypeEnum targetType) {
        this.targetType = targetType;
    }

    public List<?> getTargetList() {
        return targetList;
    }

    public void setTargetList(List<?> targetList) {
        this.targetList = targetList;
    }

    public List<String> getAuthToolList() {
        return authToolList;
    }

    public void setAuthToolList(List<String> authToolList) {
        this.authToolList = authToolList;
    }
}
