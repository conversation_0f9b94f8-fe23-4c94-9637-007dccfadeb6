package com.yxt.dynamic.routing.proxy;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/20 15:38
 */
@Slf4j
public class DynamicRocketMQInterceptor {
    private static DynamicRocketMQInterceptor instance;
    private DynamicProperties dynamicProperties;

    public static DynamicRocketMQInterceptor getInstance() {
        if (instance == null) {
            synchronized (DynamicRocketMQInterceptor.class) {
                if (instance == null) {
                    instance = new DynamicRocketMQInterceptor();
                }
            }
        }
        return instance;
    }

    public void interceptSend(Object obj) {
        if (obj == null) {
            return;
        }
        if(obj instanceof Message){
            interceptSendMsg((Message) obj);
            return;
        }
        if(obj instanceof List){
            interceptSendListMsg((List<Message>) obj);
        }
    }

    public void interceptSendMsg(Message message) {
        if (message == null) {
            return;
        }
        setHeader(message);
    }

    public void interceptSendListMsg(List<Message> messages) {
        if (messages != null || !messages.isEmpty()) {
            for (Message message : messages) {
                setHeader(message);
            }
        }
    }

    private void setHeader(Message message) {
        DynamicProperties dynamicProperties = getDynamicProperties();
        if (dynamicProperties == null || !dynamicProperties.isEnable()) {
            return;
        }
        if (StringUtils.isNotBlank(DynamicRouteContext.getAllRoute())) {
            message.putUserProperty(DynamicRouteContext.ROUTE_KEY, DynamicRouteContext.getAllRoute());
        }
    }

    public void interceptReceive(Object obj) throws IOException {
        if(obj==null){
            return;
        }
        if(obj instanceof List){
            this.interceptReceive((List)obj);
        }
    }

    public void interceptReceive(List<MessageExt> msgs) throws IOException {
        if (CollectionUtils.isEmpty(msgs)) {
            return;
        }
        DynamicProperties dynamicProperties = getDynamicProperties();
        if (dynamicProperties == null || !dynamicProperties.isEnable()) {
            return;
        }
        MessageExt messageExt = msgs.get(0);
        String allRoute = messageExt.getProperty(DynamicRouteContext.ROUTE_KEY);
        if (StringUtils.isBlank(allRoute)) {
            return;
        }
        if (StringUtils.isNotBlank(allRoute)) {
            DynamicRouteContext.setRoute(allRoute);
        }
    }

    public DynamicProperties getDynamicProperties() {
        if (dynamicProperties == null) {
            dynamicProperties = DynamicBeanComponent.getBean(DynamicProperties.class);
        }
        return dynamicProperties;
    }


}
