{"properties": [{"name": "yxt.sentinel.transport.dashboard", "type": "java.lang.String", "description": "dashboard地址"}, {"name": "yxt.sentinel.datasource.namespace", "type": "java.lang.String", "description": "nacos持久化命名空间"}, {"name": "yxt.sentinel.datasource.group-id", "type": "java.lang.String", "description": "nacos持久化group-id"}, {"name": "yxt.sentinel.auth.wechat-tag-list", "type": "java.util.List", "description": "企业微信用户标签id 兼容 alarm.sendErrorLog.totag"}, {"name": "yxt.sentinel.auth.user-id-list", "type": "java.util.List", "description": "用户工号 兼容alarm.sendErrorLog.touser"}]}