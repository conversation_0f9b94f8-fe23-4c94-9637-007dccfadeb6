package com.yxt.mcp.server.starter.support.datasource.source.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.yxt.mcp.server.starter.support.datasource.listener.McpDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.source.MCPDataSource;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;

/**
 * <AUTHOR>
 * @Description 工具变更协议
 * @Date 2025/06/12/16:48
 */
@Slf4j
public class AbstractNacosDataSource<T> implements MCPDataSource<T>, InitializingBean {


    private ConfigService configService;

    private String dataId;

    private String group;

    private String serverAddr;

    private String namespace;

    private Integer timeout;

    private List<? extends McpDataSourceListener> mcpDataSourceListenerList;

    // 增加一个 TypeReference<T> 字段用于保存泛型信息
    private final TypeReference<T> typeReference;

    public AbstractNacosDataSource(
        List<? extends McpDataSourceListener> mcpDataSourceListenerList, String serverAddr, String namespace,
        String group,
        String dataId, Integer timeout, TypeReference<T> typeReference) {

        this.mcpDataSourceListenerList = mcpDataSourceListenerList;
        this.serverAddr = serverAddr;
        this.namespace = namespace;
        this.group = group;
        this.dataId = dataId;
        this.timeout = timeout;
        this.typeReference = typeReference;
    }

    @Override
    public T read() {
        try {
            String content = configService.getConfig(dataId, group, timeout);
            if (log.isDebugEnabled()) {
                log.debug("获取到的配置内容：dataId={}, group={}, content={}", dataId, group, content);
            }

            if (content == null || content.trim().isEmpty()) {
                log.warn("配置内容为空或无效，dataId={}, group={}", dataId, group);
                return null;
            }

            // 判断是否是 String 类型
            if (typeReference.getType() instanceof Class) {
                Class<?> clazz = (Class<?>) typeReference.getType();
                if (String.class.isAssignableFrom(clazz)) {
                    return (T) content;
                }
            }

            // 使用 FastJSON 反序列化为泛型类型
            return JSON.parseObject(content, typeReference);
        } catch (NacosException e) {
            log.error("读取 Nacos 配置失败 dataId={}, group={}", dataId, group, e);
        }
        return null;
    }

    @Override
    public void afterPropertiesSet() {
        Properties properties = new Properties();
        // 设置 Nacos 服务器地址
        properties.put("serverAddr", serverAddr);
        // 设置命名空间ID（可选，默认是空）
        properties.put("namespace", namespace);
        // 设置超时时间（可选）
        properties.put("timeout", timeout);

        try {
            this.configService = NacosFactory.createConfigService(properties);

            // 获取配置

            // 可选：添加监听器，当配置发生变化时触发回调
            configService.addListener(dataId, group,
                new Listener() {
                    @Override
                    public Executor getExecutor() {
                        return null;
                    }

                    @Override
                    public void receiveConfigInfo(String s) {
                        if (log.isDebugEnabled()) {
                            log.debug("配置已更新contest={}", s);
                        }
                        log.info("监听到配置变更..... dataId={},group={}", dataId,
                            group);
                        if (CollectionUtils.isNotEmpty(mcpDataSourceListenerList)) {
                            mcpDataSourceListenerList.forEach(listener -> {
                                try {
                                    listener.notifyChange(s);
                                } catch (Exception e) {
                                    log.error("配置变更监听器处理异常", e);
                                }
                            });
                        }
                    }
                });
        } catch (NacosException e) {
            log.error("nacos 通信异常 dataId={},group={}", dataId, group, e);
        }
    }

}
