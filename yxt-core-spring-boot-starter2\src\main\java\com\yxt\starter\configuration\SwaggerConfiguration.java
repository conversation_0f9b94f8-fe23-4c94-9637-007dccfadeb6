//package com.yxt.starter.configuration;
//
//import com.google.common.base.Predicate;
//import com.google.common.base.Predicates;
//import com.google.common.collect.Lists;
//import java.util.Collections;
//import java.util.List;
//import java.util.stream.Collectors;
//import lombok.NoArgsConstructor;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import springfox.documentation.RequestHandler;
//import springfox.documentation.builders.ApiInfoBuilder;
//import springfox.documentation.builders.PathSelectors;
//import springfox.documentation.builders.RequestHandlerSelectors;
//import springfox.documentation.service.ApiInfo;
//import springfox.documentation.service.ApiKey;
//import springfox.documentation.service.AuthorizationScope;
//import springfox.documentation.service.Contact;
//import springfox.documentation.service.SecurityReference;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spi.service.contexts.SecurityContext;
//import springfox.documentation.spring.web.plugins.Docket;
//import springfox.documentation.swagger2.annotations.EnableSwagger2;
//
// todo:swagger目前无法支持springboot3.x暂时注释
//@Configuration
//@EnableSwagger2
//public class SwaggerConfiguration {
//
//    @Value("${swagger.enable:false}")
//    private Boolean enable;
//
//    @Value("${swagger.basePackage:com.yxt}")
//    private String basePackage;
//
//    @Bean
//    public Docket yxtTasktDocket() {
//        return new DocketBuilder()
//            .title("Restful API接口文档")
//            .description("Restful API Interface Docs.")
//            .basePackages(basePackage)
//            .newDocket(enable);
//    }
//
//    @NoArgsConstructor
//    public static class DocketBuilder {
//
//        private String groupName;
//        private String title;
//        private String description;
//        private String[] basePackages;
//
//        public DocketBuilder basePackages(String... basePackages) {
//            this.basePackages = basePackages;
//            return this;
//        }
//
//        public DocketBuilder groupName(String groupName) {
//            this.groupName = groupName;
//            return this;
//        }
//
//        public DocketBuilder title(String title) {
//            this.title = title;
//            return this;
//        }
//
//        public DocketBuilder description(String description) {
//            this.description = description;
//            return this;
//        }
//
//        public Docket newDocket(Boolean enable) {
//            List<Predicate<RequestHandler>> collect = Lists.newArrayList(this.basePackages).stream()
//                .map(RequestHandlerSelectors::basePackage).collect(Collectors.toList());
//
//            return new Docket(DocumentationType.SWAGGER_2)
//                .groupName(this.groupName)
//                .apiInfo(apiInfo(this.title, this.description))
//                .securityContexts(Lists.newArrayList(securityContext()))
//                .securitySchemes(Lists.newArrayList(new ApiKey("JWT", "Authorization", "header"),
//                    new ApiKey("员工编号", "empCode", "header"),
//                    new ApiKey("用户", "userId", "header")))
//                .enable(enable)
//                .select()
//                .apis(Predicates.or(collect))
//                .paths(PathSelectors.any())
//                .build();
//        }
//
//        private ApiInfo apiInfo(String title, String description) {
//            return new ApiInfoBuilder()
//                .title(title)
//                .description(description)
//                .contact(new Contact("", "", ""))
//                .build();
//        }
//
//        private SecurityContext securityContext() {
//            AuthorizationScope authorizationScope
//                = new AuthorizationScope("global", "accessEverything");
//            AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
//            authorizationScopes[0] = authorizationScope;
//            List<SecurityReference> defaultAuth = Collections.singletonList(
//                new SecurityReference("JWT", authorizationScopes));
//
//            return SecurityContext.builder().securityReferences(defaultAuth).build();
//        }
//    }
//}
