package com.yxt.mcp.server.starter.support.datasource.source.impl;

import com.alibaba.fastjson2.TypeReference;
import com.yxt.mcp.server.starter.support.datasource.listener.McpToolDataSourceListener;
import com.yxt.mcp.server.starter.support.datasource.source.MCPToolDataSource;
import com.yxt.mcp.server.starter.support.properties.YxtNacosMcpProperties;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 工具变更协议
 * @Date 2025/06/12/16:48
 */
@Slf4j
public class NacosMCPToolDataSource extends AbstractNacosDataSource<String> implements MCPToolDataSource {


    public NacosMCPToolDataSource(YxtNacosMcpProperties yxtNacosMcpProperties,
        List<McpToolDataSourceListener> mcpDataSourceListenerList) {
        super(mcpDataSourceListenerList, yxtNacosMcpProperties.getServerAddr(), yxtNacosMcpProperties.getNamespace(),
            yxtNacosMcpProperties.getGroup(),
            yxtNacosMcpProperties.getToolDataId(), yxtNacosMcpProperties.getTimeout(), new TypeReference<String>() {
            });
    }

}
