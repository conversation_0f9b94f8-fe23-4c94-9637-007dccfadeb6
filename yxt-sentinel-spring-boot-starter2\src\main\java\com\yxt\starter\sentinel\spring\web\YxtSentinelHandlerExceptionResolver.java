package com.yxt.starter.sentinel.spring.web;

import com.alibaba.csp.sentinel.adapter.spring.webmvc_v6x.callback.BlockExceptionHandler;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.lang.reflect.UndeclaredThrowableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

/**
 * 处理代理类通过UndeclaredThrowableException 包装过的流控异常
 *
 * <AUTHOR>
 */
public class YxtSentinelHandlerExceptionResolver implements HandlerExceptionResolver, Ordered {

    private static final Logger logger = LoggerFactory.getLogger(YxtSentinelHandlerExceptionResolver.class);
    @Resource
    private BlockExceptionHandler blockExceptionHandler;

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler,
        Exception ex) {
        // 检查是否为UndeclaredThrowableException,不符合处理条件，交给后续异常处理器处理
        if (!(ex instanceof UndeclaredThrowableException)) {
            return null;
        }

        // 获取嵌套的异常
        Throwable cause = ex.getCause();
        // 检查是否为BlockException
        if (cause instanceof BlockException) {
            BlockException blockException = (BlockException) cause;
            try {
                // 调用自定义异常处理器
                // todo:添加空字符串参数适配新的方法签名,后续需要看是否需要传入其他值
                blockExceptionHandler.handle(request, response, "", blockException);
                return new ModelAndView();
            } catch (Exception e) {
                logger.error("#42 YxtSentinelHandlerExceptionResolver 处理流控异常失败", e);
                // 返回NULL  交给后续异常处理器处理
                return null;
            }
        }
        // 不符合处理条件，交给后续异常处理器处理
        return null;
    }

    @Override
    public int getOrder() {
        //优先级最高，避免流控异常被别的异常处理器处理
        return Integer.MIN_VALUE;
    }
}
