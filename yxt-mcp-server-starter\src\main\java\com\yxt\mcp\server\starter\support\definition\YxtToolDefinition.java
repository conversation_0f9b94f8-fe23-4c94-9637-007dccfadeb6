package com.yxt.mcp.server.starter.support.definition;

import com.alibaba.fastjson2.JSONObject;
import com.yxt.mcp.server.starter.support.constants.ToolProtocolEnum;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @Description yxt 工具定义 元数据
 * @Date 2025/06/12/16:14
 */
public class YxtToolDefinition implements ToolDefinition {

    /**
     * 工具名称
     */
    private String name;

    /**
     * 工具描述
     */
    private String description;

    /**
     * 工具元数据
     */
    private String inputSchema;

    /**
     * 工具协议元数据
     */
    private JSONObject protocolMeta;

    /**
     * 工具协议
     */
    private ToolProtocolEnum protocol;

    public YxtToolDefinition(final String name, final String description, final String inputSchema,
        ToolProtocolEnum protocol, JSONObject protocolMeta) {
        Assert.hasText(name, "name cannot be null or empty");
        Assert.hasText(description, "description cannot be null or empty");
        Assert.hasText(inputSchema, "inputSchema cannot be null or empty");
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
        this.protocol = protocol;
        this.protocolMeta = protocolMeta;
    }

    @Override
    public String name() {
        return name;
    }

    @Override
    public String description() {
        return description;
    }

    @Override
    public String inputSchema() {
        return inputSchema;
    }


    public ToolProtocolEnum getProtocol() {
        return protocol;
    }

    public JSONObject getProtocolMeta() {
        return protocolMeta;
    }


}
