package com.yxt.starter.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

public class Cache {

    /**
     * zkClient Cache
     */
    public static Map<String, ConfigService> nacosclient_cache = new HashMap<>();

    /**
     * 获取nacos配置操作客户端
     * @param properties
     * @return
     * @throws NacosException
     */
    public static ConfigService getNacosConfigClient(Properties properties) throws NacosException {
        if(Objects.nonNull(nacosclient_cache.get(properties.getProperty(PropertyKeyConst.NAMESPACE)))){
            return nacosclient_cache.get(properties.getProperty(PropertyKeyConst.NAMESPACE));
        }
        ConfigService namingService = NacosFactory.createConfigService(properties);
        nacosclient_cache.put(properties.getProperty(PropertyKeyConst.NAMESPACE),namingService);
        return namingService;
    }

}
