package com.yxt.starter.configuration;

import com.yxt.starter.log.oplog.OpLogAspect;
import jakarta.servlet.http.HttpServlet;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;



@Configuration
public class LogConfiguration {

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(HttpServlet.class)
    public OpLogAspect buildOpLogAspect() {
        return new OpLogAspect();
    }
}
