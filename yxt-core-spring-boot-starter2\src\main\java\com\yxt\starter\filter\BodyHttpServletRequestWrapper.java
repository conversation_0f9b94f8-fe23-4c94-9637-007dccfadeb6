package com.yxt.starter.filter;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;


import java.io.*;
import org.apache.commons.io.IOUtils;

/**
 * 请求数据拦截
 * <AUTHOR>
 */
public class BodyHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private byte[] body;
    private ServletInputStreamWrapper inputStreamWrapper;

    public BodyHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        int length = request.getContentLength();
        InputStream inputStream = request.getInputStream();
        // todo:源码
        //this.body = IOUtils.readFully(inputStream,length,true);
        this.body = IOUtils.readFully(inputStream,length);
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.body);
        this.inputStreamWrapper = new ServletInputStreamWrapper(byteArrayInputStream);
        resetInputStream();
    }

    private void resetInputStream() {
        this.inputStreamWrapper.setInputStream(new ByteArrayInputStream(this.body != null ? this.body : new byte[0]));
    }

    public byte[] getBody() {
        return body;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return this.inputStreamWrapper;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.inputStreamWrapper));
    }

    private static class ServletInputStreamWrapper extends ServletInputStream {

        private InputStream inputStream;

        public InputStream getInputStream() {
            return inputStream;
        }

        public void setInputStream(InputStream inputStream) {
            this.inputStream = inputStream;
        }

        public ServletInputStreamWrapper(InputStream inputStream) {
            this.inputStream = inputStream;
        }

        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setReadListener(ReadListener readListener) {

        }

        @Override
        public int read() throws IOException {
            return this.inputStream.read();
        }
    }
}
