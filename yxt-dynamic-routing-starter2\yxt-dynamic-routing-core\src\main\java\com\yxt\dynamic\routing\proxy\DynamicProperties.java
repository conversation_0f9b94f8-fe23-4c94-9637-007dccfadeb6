package com.yxt.dynamic.routing.proxy;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 18:09
 */
@ConfigurationProperties("dynamic")
@RefreshScope
@Data
public class DynamicProperties {

    private boolean enable;

    /**
     * 本地服务映射配置
     * Key: 源服务名或正则表达式
     * Value: 目标服务名、IP:端口或正则替换表达式
     */
    private Map<String, String> localMappings;

}
