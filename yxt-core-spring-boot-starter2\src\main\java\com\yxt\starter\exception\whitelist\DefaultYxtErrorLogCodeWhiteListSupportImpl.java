package com.yxt.starter.exception.whitelist;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 默认告警errorCode白名单
 *
 * <AUTHOR>
 * @date 2024/01/22 11:56
 **/
@Service
public class DefaultYxtErrorLogCodeWhiteListSupportImpl implements YxtErrorLogCodeWhiteListSupport {


    @Override
    public List<ErrorCodeAlarmWhiteCode> listErrorCodeAlarmWhiteCode() {
        return Collections.emptyList();
    }
}
