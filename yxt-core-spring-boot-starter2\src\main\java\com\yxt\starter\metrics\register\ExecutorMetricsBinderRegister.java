package com.yxt.starter.metrics.register;

import com.yxt.starter.metrics.MetricsBinderRegister;
import com.yxt.starter.metrics.MyMetricsProperties;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;

public class ExecutorMetricsBinderRegister implements MetricsBinderRegister {

    @Override
    public void registry(MeterRegistry meterRegistry, ApplicationContext context) {
        MyMetricsProperties myMetricsProperties = context.getBean(MyMetricsProperties.class);
        Map<String, ThreadPoolTaskExecutor> threadPoolTaskExecutorMap = context.getBeansOfType(ThreadPoolTaskExecutor.class);
        if (threadPoolTaskExecutorMap.isEmpty()) {
            return;
        }
        threadPoolTaskExecutorMap.entrySet().stream().filter(entry -> {
            List<String> includeThreadPoolBeanNames = myMetricsProperties.getIncludeThreadPoolBeanNames();
            if (includeThreadPoolBeanNames != null && !includeThreadPoolBeanNames.isEmpty()) {
                return includeThreadPoolBeanNames.contains(entry.getKey());
            }
            List<String> ignoreThreadPoolBeanNames = myMetricsProperties.getIgnoreThreadPoolBeanNames();
            if (ignoreThreadPoolBeanNames != null && !ignoreThreadPoolBeanNames.isEmpty()) {
                return !ignoreThreadPoolBeanNames.contains(entry.getKey());
            }
            return true;
        }).forEach(entry -> {
            ExecutorServiceMetrics executorServiceMetrics = new ExecutorServiceMetrics(entry.getValue().getThreadPoolExecutor(), entry.getKey(), Tags.empty());
            executorServiceMetrics.bindTo(meterRegistry);
        });
    }
}
