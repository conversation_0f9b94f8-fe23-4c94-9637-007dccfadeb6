package com.yxt.mcp.server.starter.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yxt.mcp.server.starter.auth.AuthorizationService;
import com.yxt.mcp.server.starter.entrance.provider.YxtWebFluxSseServerTransportProvider;
import com.yxt.mcp.server.starter.support.session.YxtSessionFactory;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.server.transport.WebFluxSseServerTransportProvider;
import io.modelcontextprotocol.spec.McpSchema.Root;
import io.modelcontextprotocol.spec.McpServerSession;
import io.modelcontextprotocol.spec.McpServerTransportProvider;

import java.util.List;
import java.util.function.BiConsumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.server.autoconfigure.McpServerProperties;
import org.springframework.ai.mcp.server.autoconfigure.McpServerStdioDisabledCondition;
import org.springframework.ai.mcp.server.autoconfigure.McpWebFluxServerAutoConfiguration;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Import;
import org.springframework.web.reactive.function.server.RouterFunction;

/**
 * <AUTHOR>
 * @Description 自定义YxtWebFluxSseServerTransportProvider
 * @Date 2025/06/09/13:49
 */
@AutoConfiguration
@ConditionalOnClass({WebFluxSseServerTransportProvider.class})
@ConditionalOnMissingBean(McpServerTransportProvider.class)
@Conditional(McpServerStdioDisabledCondition.class)
@AutoConfigureBefore(McpWebFluxServerAutoConfiguration.class)
@Import(YxtWebFluxSseServerTransportProviderCustomConfig.class)
@Slf4j
public class YxtWebFluxSseServerTransportProviderAutoConfig {


    /**
     * 创建一个自定义的 MCP Server Transport Provider
     * objectMapperProvider: 提供 JSON 序列化/反序列化的 ObjectMapper 实例。
     * serverProperties: MCP Server 配置属性。
     * authorizationService: 授权服务，用于处理请求的权限校验。
     * sessionFactory: 创建 MCP Server Session 的工厂。
     * 逻辑说明：
     * 使用提供的配置构造 YxtWebFluxSseServerTransportProvider。
     * 设置 Session 工厂以支持会话管理。
     */
    @Bean
    public McpServerTransportProvider mcpServerTransportProvider(
            ObjectProvider<ObjectMapper> objectMapperProvider,
            McpServerProperties serverProperties,
            AuthorizationService authorizationService, McpServerSession.Factory sessionFactory) {
        ObjectMapper objectMapper = objectMapperProvider.getIfAvailable(ObjectMapper::new);
        YxtWebFluxSseServerTransportProvider webFluxSseServerTransportProvider = new YxtWebFluxSseServerTransportProvider(
                objectMapper,
                serverProperties.getBaseUrl(),
                serverProperties.getSseMessageEndpoint(), serverProperties.getSseEndpoint(), authorizationService);
        webFluxSseServerTransportProvider.setSessionFactory(sessionFactory);
        return webFluxSseServerTransportProvider;
    }


    /**
     * 创建一个 WebFlux SSE 路由函数，用于处理 MCP 请求。
     * mcpServerTransportProvider: MCP Server Transport Provider，用于处理 MCP 请求。
     * 逻辑说明：
     * 如果 MCP Server Transport Provider 是 YxtWebFluxSseServerTransportProvider 类型，则返回其路由函数。
     * 否则，返回 null。
     */
    @Bean
    public RouterFunction<?> webfluxMcpRouterFunction(McpServerTransportProvider mcpServerTransportProvider) {
        if (mcpServerTransportProvider instanceof YxtWebFluxSseServerTransportProvider) {
            return ((YxtWebFluxSseServerTransportProvider) mcpServerTransportProvider).getRouterFunction();
        }
        return null;
    }


    /**
     * 创建一个 MCP Server Session 的工厂。
     * rootsChangeConsumers: 提供根节点变更事件的消费者。
     * serverProperties: MCP Server 配置属性。
     * authorizationService: 授权服务，用于处理请求的权限校验。
     * 逻辑说明：
     * 创建一个 YxtSessionFactory 实例，并设置根节点变更事件的消费者、MCP Server 配置属性和授权服务。
     */
    @Bean
    @ConditionalOnMissingBean(McpServerSession.Factory.class)
    public McpServerSession.Factory sessionFactory(
            ObjectProvider<BiConsumer<McpSyncServerExchange, List<Root>>> rootsChangeConsumers,
            McpServerProperties serverProperties, AuthorizationService authorizationService) {
        return new YxtSessionFactory(rootsChangeConsumers, serverProperties, authorizationService);
    }

}
