package com.yxt.mcp.server.starter.support.datasource.listener.impl;

import com.yxt.mcp.server.starter.support.datasource.listener.McpToolDataSourceListener;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinitionService;
import com.yxt.mcp.server.starter.support.provider.YxtToolsProvider;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description 工具变更监听器
 * @Date 2025/06/12/18:16
 */

public class YxtMcpToolListener implements McpToolDataSourceListener {

    private static final Logger logger = LoggerFactory.getLogger(YxtMcpToolListener.class);

    private YxtToolsProvider yxtToolsProvider;

    private YxtToolDefinitionService yxtToolDefinitionService;

    public YxtMcpToolListener(YxtToolsProvider yxtToolsProvider, YxtToolDefinitionService yxtToolDefinitionService) {
        this.yxtToolsProvider = yxtToolsProvider;
        this.yxtToolDefinitionService = yxtToolDefinitionService;
    }


    @Override
    public void notifyChange(String content) {
        List<YxtToolDefinition> yxtToolDefinitions = yxtToolDefinitionService.listYxtToolDefinition();
        for (YxtToolDefinition yxtToolDefinition : yxtToolDefinitions) {
            yxtToolsProvider.addTool(yxtToolDefinition);
        }
        if (logger.isDebugEnabled()) {
            logger.debug("工具变更监听器监听到工具变更，content={}", content);
        }
        logger.info("工具变更监听器监听到工具变更");
    }
}
