<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yxt-xframe2</artifactId>
        <groupId>com.yxt</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <name>yxt-sentinel-spring-boot-starter2</name>
    <version>2.0.0</version>
    <artifactId>yxt-sentinel-spring-boot-starter2</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 升级到支持Spring Boot 3.5.0的版本 -->
        <spring.cloud.sentinel.version>2023.0.3.2</spring.cloud.sentinel.version>
        <sentinel.datasource.nacos.version>1.8.8</sentinel.datasource.nacos.version>
        <!-- 升级PowerMock以支持JDK21 -->
        <powermock.version>2.0.9</powermock.version>
        <!-- 打包配置 -->
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <groupId>com.alibaba.cloud</groupId>
            <version>${spring.cloud.sentinel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--nacos持久化-->
        <dependency>
            <artifactId>sentinel-datasource-nacos</artifactId>
            <groupId>com.alibaba.csp</groupId>
            <version>${sentinel.datasource.nacos.version}</version>
        </dependency>
        <dependency>
            <artifactId>spring-boot-starter-web</artifactId>
            <groupId>org.springframework.boot</groupId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <artifactId>spring-cloud-context</artifactId>
            <groupId>org.springframework.cloud</groupId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-common-lang2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--Open feign-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!--powermock开始 mockito 扩展,支持mock静态方法、私有方法-->
        <dependency>
            <artifactId>powermock-module-junit4</artifactId>
            <groupId>org.powermock</groupId>
            <scope>test</scope>
            <version>${powermock.version}</version>
        </dependency>
        <dependency>
            <artifactId>powermock-api-mockito2</artifactId>
            <groupId>org.powermock</groupId>
            <scope>test</scope>
            <version>${powermock.version}</version>
        </dependency>

        <!-- Apache Commons集合工具 - 支持JDK21 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!--json序列化-->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--发布源码插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>
</project>
