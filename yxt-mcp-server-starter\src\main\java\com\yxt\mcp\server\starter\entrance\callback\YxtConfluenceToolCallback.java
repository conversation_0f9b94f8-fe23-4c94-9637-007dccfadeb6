package com.yxt.mcp.server.starter.entrance.callback;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.yxt.mcp.server.starter.auth.AuthorizationService.YxtAuthResult;
import com.yxt.mcp.server.starter.support.channel.sdk.ConfluenceRestClient;
import com.yxt.mcp.server.starter.support.constants.McpServerConstants;
import com.yxt.mcp.server.starter.support.definition.YxtToolDefinition;
import java.util.Map;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @Description 一心堂cf工具处理器
 * @Date 2025/06/12/16:19
 */
public class YxtConfluenceToolCallback extends AbstractToolCallback {


    public YxtConfluenceToolCallback(YxtToolDefinition toolDefinition, ApplicationContext applicationContext) {
        super(toolDefinition, applicationContext);
    }


    @Override
    public String process(YxtAuthResult yxtAuthResult, String toolInput) {
        // 获取原始 URL 模板
        String urlTemplate = getYxtToolDefinition().getProtocolMeta()
            .getString(McpServerConstants.FIELD_PROTOCOL_META_URL);

        // 将 JSON 字符串解析为 Map
        Map<String, Object> valuesMap = parseJsonToMap(toolInput);
        // 使用模板引擎替换变量
        String processedUrl = StringSubstitutor.replace(urlTemplate, valuesMap);

        return getBean(ConfluenceRestClient.class).getByUrl(processedUrl);
    }


    /**
     * 将 JSON 字符串转换为 Map
     */
    private Map<String, Object> parseJsonToMap(String json) {
        try {
            return JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid toolInput JSON: " + json, e);
        }
    }
}
