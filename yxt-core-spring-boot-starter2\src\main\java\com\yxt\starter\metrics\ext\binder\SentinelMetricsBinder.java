package com.yxt.starter.metrics.ext.binder;

import com.alibaba.csp.sentinel.Constants;
import com.alibaba.csp.sentinel.node.DefaultNode;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import io.micrometer.core.lang.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SentinelMetricsBinder implements MeterBinder {

    @Override
    public void bindTo(@NonNull MeterRegistry meterRegistry) {
        Gauge.builder("sentinel.threadNum", Constants.ROOT, DefaultNode::curThreadNum)
                .tags("threadNum", Constants.ROOT.getId().getShowName())
                .description("This Server threadNum")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.passQps", Constants.ROOT, node -> (long) node.passQps())
                .tags("passQps", Constants.ROOT.getId().getShowName())
                .description("This Server passQps")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.blockQps", Constants.ROOT, node -> (long) node.blockQps())
                .tags("blockQps", Constants.ROOT.getId().getShowName())
                .description("This Server blockQps")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.totalQps", Constants.ROOT, node -> (long) node.totalQps())
                .tags("totalQps", Constants.ROOT.getId().getShowName())
                .description("This Server totalQps")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.averageRt", Constants.ROOT, node -> (long) node.avgRt())
                .tags("averageRt", Constants.ROOT.getId().getShowName())
                .description("This Server AvgRt")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.successQps", Constants.ROOT, node -> (long) node.successQps())
                .tags("successQps", Constants.ROOT.getId().getShowName())
                .description("This Server successQps")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.exceptionQps", Constants.ROOT, node -> (long) node.exceptionQps())
                .tags("exceptionQps", Constants.ROOT.getId().getShowName())
                .description("This Server exceptionQps")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.oneMinuteException", Constants.ROOT, DefaultNode::totalException)
                .tags("oneMinuteException", Constants.ROOT.getId().getShowName())
                .description("This Server oneMinuteException")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.oneMinutePass", Constants.ROOT, node -> node.totalRequest() - node.blockRequest())
                .tags("oneMinutePass", Constants.ROOT.getId().getShowName())
                .description("This Server oneMinutePass")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.oneMinuteBlock", Constants.ROOT, DefaultNode::blockRequest)
                .tags("oneMinuteBlock", Constants.ROOT.getId().getShowName())
                .description("This Server oneMinuteBlock")
                .register(meterRegistry).measure();
        Gauge.builder("sentinel.oneMinuteTotal", Constants.ROOT, DefaultNode::totalRequest)
                .tags("oneMinuteTotal", Constants.ROOT.getId().getShowName())
                .description("This Server oneMinuteTotal")
                .register(meterRegistry).measure();

        List<Double> versionList = getJvmVersion();
        Gauge.builder("jvm.majorVersion", versionList, list -> list.get(0))
                .description("This JDK majorVersion")
                .register(meterRegistry).measure();
        Gauge.builder("jvm.minorVersion", versionList, list -> list.get(1))
                .description("This JDK minorVersion")
                .register(meterRegistry).measure();
    }

    private List<Double> getJvmVersion() {
        List<Double> versionList = new ArrayList<>(2);
        try {
            String jvmVersion = System.getProperty("java.version");
            String regex = "(^\\d+\\.\\d+)(.*)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(jvmVersion);
            if (matcher.find()) {
                String majorVersion = matcher.group(1);
                versionList.add(0, Double.valueOf(majorVersion));
                String minorVersion = "0" + matcher.group(2);
                Pattern p = Pattern.compile("_");
                Matcher m = p.matcher(minorVersion);
                minorVersion = m.replaceAll("");
                versionList.add(1, Double.valueOf(minorVersion));
            }
        } catch (Exception e) {
            versionList.add(0, -1.0D);
            versionList.add(1, -1.0D);
        }
        return versionList;
    }

}
